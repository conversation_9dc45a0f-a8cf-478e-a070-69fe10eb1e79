{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/shared/constants/index.ts"], "sourcesContent": ["/**\n * 应用常量定义\n */\n\n// ============================================================================\n// 任务相关常量\n// ============================================================================\n\nexport const TASK_CATEGORIES = {\n  WORK: 'work',\n  IMPROVEMENT: 'improvement',\n  ENTERTAINMENT: 'entertainment'\n} as const;\n\nexport const TASK_STATUSES = {\n  PENDING: 'pending',\n  IN_PROGRESS: 'in-progress',\n  COMPLETED: 'completed',\n  POSTPONED: 'postponed'\n} as const;\n\nexport const PRIORITY_LEVELS = {\n  VERY_LOW: 1,\n  LOW: 2,\n  MEDIUM: 3,\n  HIGH: 4,\n  VERY_HIGH: 5\n} as const;\n\nexport const QUADRANTS = {\n  URGENT_IMPORTANT: 1,      // 重要且紧急\n  IMPORTANT_NOT_URGENT: 2,  // 重要不紧急\n  URGENT_NOT_IMPORTANT: 3,  // 紧急不重要\n  NOT_URGENT_NOT_IMPORTANT: 4 // 不紧急不重要\n} as const;\n\n// ============================================================================\n// 时间相关常量\n// ============================================================================\n\nexport const TIME_CONSTANTS = {\n  MINUTES_PER_HOUR: 60,\n  HOURS_PER_DAY: 24,\n  DAYS_PER_WEEK: 7,\n  DEFAULT_BREAK_DURATION: 15, // 分钟\n  DEFAULT_TASK_DURATION: 60,  // 分钟\n  MIN_TASK_DURATION: 15,      // 分钟\n  MAX_TASK_DURATION: 480      // 分钟 (8小时)\n} as const;\n\nexport const WORK_DAYS = [\n  'monday',\n  'tuesday', \n  'wednesday',\n  'thursday',\n  'friday'\n] as const;\n\nexport const ALL_DAYS = [\n  'monday',\n  'tuesday',\n  'wednesday', \n  'thursday',\n  'friday',\n  'saturday',\n  'sunday'\n] as const;\n\nexport const DAY_NAMES_CN = {\n  monday: '周一',\n  tuesday: '周二',\n  wednesday: '周三',\n  thursday: '周四',\n  friday: '周五',\n  saturday: '周六',\n  sunday: '周日'\n} as const;\n\n// ============================================================================\n// 默认配置\n// ============================================================================\n\nexport const DEFAULT_USER_CONFIG = {\n  workStart: '09:00',\n  workEnd: '18:00',\n  workDays: WORK_DAYS,\n  sleepStart: '23:00',\n  sleepEnd: '07:00',\n  fixedSlots: [\n    { start: '07:00', end: '08:00', type: 'personal', label: '晨间例行' },\n    { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' },\n    { start: '18:30', end: '19:30', type: 'meal', label: '晚餐时间' }\n  ],\n  commuteToWork: 30,\n  commuteFromWork: 30,\n  preferredWorkHours: 'morning' as const,\n  maxContinuousWork: 120,\n  breakInterval: 25,\n  categoryPreferences: {\n    work: { \n      preferredTimes: ['09:00-12:00', '14:00-18:00'], \n      maxDaily: 480 \n    },\n    improvement: { \n      preferredTimes: ['07:00-09:00', '19:00-21:00'], \n      maxDaily: 120 \n    },\n    entertainment: { \n      preferredTimes: ['20:00-22:00'], \n      maxDaily: 180 \n    }\n  }\n} as const;\n\n// ============================================================================\n// 算法相关常量\n// ============================================================================\n\nexport const ALGORITHM_CONSTANTS = {\n  // 四象限权重\n  QUADRANT_WEIGHTS: {\n    [QUADRANTS.URGENT_IMPORTANT]: 1.0,\n    [QUADRANTS.IMPORTANT_NOT_URGENT]: 0.8,\n    [QUADRANTS.URGENT_NOT_IMPORTANT]: 0.6,\n    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: 0.4\n  },\n  \n  // 推迟惩罚系数\n  POSTPONE_PENALTY: 0.1,\n  \n  // 截止日期权重\n  DEADLINE_WEIGHT: 0.3,\n  \n  // 分类权重\n  CATEGORY_WEIGHTS: {\n    [TASK_CATEGORIES.WORK]: 1.0,\n    [TASK_CATEGORIES.IMPROVEMENT]: 0.8,\n    [TASK_CATEGORIES.ENTERTAINMENT]: 0.6\n  }\n} as const;\n\n// ============================================================================\n// UI 相关常量\n// ============================================================================\n\nexport const UI_CONSTANTS = {\n  // 颜色主题\n  COLORS: {\n    PRIMARY: '#4F46E5',\n    SUCCESS: '#10B981',\n    WARNING: '#F59E0B',\n    ERROR: '#EF4444',\n    INFO: '#3B82F6'\n  },\n  \n  // 象限颜色\n  QUADRANT_COLORS: {\n    [QUADRANTS.URGENT_IMPORTANT]: {\n      bg: 'bg-red-100',\n      border: 'border-red-300',\n      text: 'text-red-800',\n      selectedBg: 'bg-red-200',\n      selectedBorder: 'border-red-500'\n    },\n    [QUADRANTS.IMPORTANT_NOT_URGENT]: {\n      bg: 'bg-blue-100',\n      border: 'border-blue-300', \n      text: 'text-blue-800',\n      selectedBg: 'bg-blue-200',\n      selectedBorder: 'border-blue-500'\n    },\n    [QUADRANTS.URGENT_NOT_IMPORTANT]: {\n      bg: 'bg-yellow-100',\n      border: 'border-yellow-300',\n      text: 'text-yellow-800', \n      selectedBg: 'bg-yellow-200',\n      selectedBorder: 'border-yellow-500'\n    },\n    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: {\n      bg: 'bg-gray-100',\n      border: 'border-gray-300',\n      text: 'text-gray-800',\n      selectedBg: 'bg-gray-200', \n      selectedBorder: 'border-gray-500'\n    }\n  },\n  \n  // 分页\n  DEFAULT_PAGE_SIZE: 20,\n  MAX_PAGE_SIZE: 100\n} as const;\n\n// ============================================================================\n// 错误代码\n// ============================================================================\n\nexport const ERROR_CODES = {\n  // 通用错误\n  UNKNOWN_ERROR: 'UNKNOWN_ERROR',\n  VALIDATION_ERROR: 'VALIDATION_ERROR',\n  NETWORK_ERROR: 'NETWORK_ERROR',\n  \n  // 认证错误\n  UNAUTHORIZED: 'UNAUTHORIZED',\n  FORBIDDEN: 'FORBIDDEN',\n  \n  // 任务相关错误\n  TASK_NOT_FOUND: 'TASK_NOT_FOUND',\n  TASK_CREATION_FAILED: 'TASK_CREATION_FAILED',\n  TASK_UPDATE_FAILED: 'TASK_UPDATE_FAILED',\n  \n  // 用户配置错误\n  USER_CONFIG_NOT_FOUND: 'USER_CONFIG_NOT_FOUND',\n  USER_CONFIG_INVALID: 'USER_CONFIG_INVALID',\n  \n  // 算法错误\n  ALGORITHM_EXECUTION_FAILED: 'ALGORITHM_EXECUTION_FAILED',\n  SCHEDULE_GENERATION_FAILED: 'SCHEDULE_GENERATION_FAILED'\n} as const;\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;;;;;;;;;;;;;;;AAExE,MAAM,kBAAkB;IAC7B,MAAM;IACN,aAAa;IACb,eAAe;AACjB;AAEO,MAAM,gBAAgB;IAC3B,SAAS;IACT,aAAa;IACb,WAAW;IACX,WAAW;AACb;AAEO,MAAM,kBAAkB;IAC7B,UAAU;IACV,KAAK;IACL,QAAQ;IACR,MAAM;IACN,WAAW;AACb;AAEO,MAAM,YAAY;IACvB,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;IACtB,0BAA0B,EAAE,SAAS;AACvC;AAMO,MAAM,iBAAiB;IAC5B,kBAAkB;IAClB,eAAe;IACf,eAAe;IACf,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,mBAAmB,IAAS,WAAW;AACzC;AAEO,MAAM,YAAY;IACvB;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,WAAW;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,eAAe;IAC1B,QAAQ;IACR,SAAS;IACT,WAAW;IACX,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;AACV;AAMO,MAAM,sBAAsB;IACjC,WAAW;IACX,SAAS;IACT,UAAU;IACV,YAAY;IACZ,UAAU;IACV,YAAY;QACV;YAAE,OAAO;YAAS,KAAK;YAAS,MAAM;YAAY,OAAO;QAAO;QAChE;YAAE,OAAO;YAAS,KAAK;YAAS,MAAM;YAAQ,OAAO;QAAO;QAC5D;YAAE,OAAO;YAAS,KAAK;YAAS,MAAM;YAAQ,OAAO;QAAO;KAC7D;IACD,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,mBAAmB;IACnB,eAAe;IACf,qBAAqB;QACnB,MAAM;YACJ,gBAAgB;gBAAC;gBAAe;aAAc;YAC9C,UAAU;QACZ;QACA,aAAa;YACX,gBAAgB;gBAAC;gBAAe;aAAc;YAC9C,UAAU;QACZ;QACA,eAAe;YACb,gBAAgB;gBAAC;aAAc;YAC/B,UAAU;QACZ;IACF;AACF;AAMO,MAAM,sBAAsB;IACjC,QAAQ;IACR,kBAAkB;QAChB,CAAC,UAAU,gBAAgB,CAAC,EAAE;QAC9B,CAAC,UAAU,oBAAoB,CAAC,EAAE;QAClC,CAAC,UAAU,oBAAoB,CAAC,EAAE;QAClC,CAAC,UAAU,wBAAwB,CAAC,EAAE;IACxC;IAEA,SAAS;IACT,kBAAkB;IAElB,SAAS;IACT,iBAAiB;IAEjB,OAAO;IACP,kBAAkB;QAChB,CAAC,gBAAgB,IAAI,CAAC,EAAE;QACxB,CAAC,gBAAgB,WAAW,CAAC,EAAE;QAC/B,CAAC,gBAAgB,aAAa,CAAC,EAAE;IACnC;AACF;AAMO,MAAM,eAAe;IAC1B,OAAO;IACP,QAAQ;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,OAAO;IACP,iBAAiB;QACf,CAAC,UAAU,gBAAgB,CAAC,EAAE;YAC5B,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,gBAAgB;QAClB;QACA,CAAC,UAAU,oBAAoB,CAAC,EAAE;YAChC,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,gBAAgB;QAClB;QACA,CAAC,UAAU,oBAAoB,CAAC,EAAE;YAChC,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,gBAAgB;QAClB;QACA,CAAC,UAAU,wBAAwB,CAAC,EAAE;YACpC,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,KAAK;IACL,mBAAmB;IACnB,eAAe;AACjB;AAMO,MAAM,cAAc;IACzB,OAAO;IACP,eAAe;IACf,kBAAkB;IAClB,eAAe;IAEf,OAAO;IACP,cAAc;IACd,WAAW;IAEX,SAAS;IACT,gBAAgB;IAChB,sBAAsB;IACtB,oBAAoB;IAEpB,SAAS;IACT,uBAAuB;IACvB,qBAAqB;IAErB,OAAO;IACP,4BAA4B;IAC5B,4BAA4B;AAC9B", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/shared/utils/dateUtils.ts"], "sourcesContent": ["/**\n * 日期和时间相关的工具函数\n */\n\nimport { TIME_CONSTANTS } from '../constants';\n\n// ============================================================================\n// 日期格式化\n// ============================================================================\n\n/**\n * 格式化日期为 YYYY-MM-DD 格式\n */\nexport function formatDate(date: Date): string {\n  return date.toISOString().split('T')[0];\n}\n\n/**\n * 格式化时间为 HH:MM 格式\n */\nexport function formatTime(date: Date): string {\n  return date.toTimeString().slice(0, 5);\n}\n\n/**\n * 格式化日期时间为 YYYY-MM-DDTHH:MM 格式（用于 datetime-local input）\n */\nexport function formatDateTime(date: Date): string {\n  return date.toISOString().slice(0, 16);\n}\n\n/**\n * 格式化持续时间为可读格式\n */\nexport function formatDuration(minutes: number): string {\n  if (minutes < TIME_CONSTANTS.MINUTES_PER_HOUR) {\n    return `${minutes}分钟`;\n  }\n  \n  const hours = Math.floor(minutes / TIME_CONSTANTS.MINUTES_PER_HOUR);\n  const remainingMinutes = minutes % TIME_CONSTANTS.MINUTES_PER_HOUR;\n  \n  if (remainingMinutes === 0) {\n    return `${hours}小时`;\n  }\n  \n  return `${hours}小时${remainingMinutes}分钟`;\n}\n\n// ============================================================================\n// 日期计算\n// ============================================================================\n\n/**\n * 获取今天的开始时间 (00:00:00)\n */\nexport function getStartOfDay(date: Date = new Date()): Date {\n  const start = new Date(date);\n  start.setHours(0, 0, 0, 0);\n  return start;\n}\n\n/**\n * 获取今天的结束时间 (23:59:59)\n */\nexport function getEndOfDay(date: Date = new Date()): Date {\n  const end = new Date(date);\n  end.setHours(23, 59, 59, 999);\n  return end;\n}\n\n/**\n * 获取明天的日期\n */\nexport function getTomorrow(date: Date = new Date()): Date {\n  const tomorrow = new Date(date);\n  tomorrow.setDate(tomorrow.getDate() + 1);\n  return tomorrow;\n}\n\n/**\n * 获取本周的开始日期（周一）\n */\nexport function getStartOfWeek(date: Date = new Date()): Date {\n  const start = new Date(date);\n  const day = start.getDay();\n  const diff = start.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始\n  start.setDate(diff);\n  return getStartOfDay(start);\n}\n\n/**\n * 获取本周的结束日期（周日）\n */\nexport function getEndOfWeek(date: Date = new Date()): Date {\n  const end = new Date(getStartOfWeek(date));\n  end.setDate(end.getDate() + 6);\n  return getEndOfDay(end);\n}\n\n/**\n * 检查两个日期是否是同一天\n */\nexport function isSameDay(date1: Date, date2: Date): boolean {\n  return formatDate(date1) === formatDate(date2);\n}\n\n/**\n * 检查日期是否是今天\n */\nexport function isToday(date: Date): boolean {\n  return isSameDay(date, new Date());\n}\n\n/**\n * 检查日期是否是明天\n */\nexport function isTomorrow(date: Date): boolean {\n  return isSameDay(date, getTomorrow());\n}\n\n/**\n * 计算两个日期之间的天数差\n */\nexport function getDaysDifference(date1: Date, date2: Date): number {\n  const timeDiff = date2.getTime() - date1.getTime();\n  return Math.ceil(timeDiff / (1000 * 3600 * 24));\n}\n\n// ============================================================================\n// 时间解析和创建\n// ============================================================================\n\n/**\n * 解析时间字符串 (HH:MM) 并创建今天的对应时间\n */\nexport function parseTimeToday(timeStr: string, baseDate: Date = new Date()): Date {\n  const [hours, minutes] = timeStr.split(':').map(Number);\n  const date = new Date(baseDate);\n  date.setHours(hours, minutes, 0, 0);\n  return date;\n}\n\n/**\n * 创建指定日期和时间的 Date 对象\n */\nexport function createDateTime(date: Date, timeStr: string): Date {\n  const [hours, minutes] = timeStr.split(':').map(Number);\n  const result = new Date(date);\n  result.setHours(hours, minutes, 0, 0);\n  return result;\n}\n\n/**\n * 获取默认的截止时间（明天下午6点）\n */\nexport function getDefaultDeadline(): Date {\n  const tomorrow = getTomorrow();\n  tomorrow.setHours(18, 0, 0, 0);\n  return tomorrow;\n}\n\n// ============================================================================\n// 时间范围检查\n// ============================================================================\n\n/**\n * 检查时间是否在指定范围内\n */\nexport function isTimeInRange(\n  time: Date, \n  startTime: string, \n  endTime: string, \n  baseDate: Date = new Date()\n): boolean {\n  const start = parseTimeToday(startTime, baseDate);\n  const end = parseTimeToday(endTime, baseDate);\n  \n  return time >= start && time <= end;\n}\n\n/**\n * 检查两个时间段是否重叠\n */\nexport function isTimeRangeOverlap(\n  start1: Date,\n  end1: Date,\n  start2: Date,\n  end2: Date\n): boolean {\n  return start1 < end2 && end1 > start2;\n}\n\n/**\n * 计算两个时间之间的分钟数\n */\nexport function getMinutesBetween(start: Date, end: Date): number {\n  return Math.round((end.getTime() - start.getTime()) / (1000 * 60));\n}\n\n// ============================================================================\n// 工作日相关\n// ============================================================================\n\n/**\n * 检查日期是否是工作日\n */\nexport function isWorkday(date: Date, workDays: string[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']): boolean {\n  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\n  const dayName = dayNames[date.getDay()];\n  return workDays.includes(dayName);\n}\n\n/**\n * 获取下一个工作日\n */\nexport function getNextWorkday(date: Date = new Date(), workDays: string[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']): Date {\n  let nextDay = new Date(date);\n  nextDay.setDate(nextDay.getDate() + 1);\n  \n  while (!isWorkday(nextDay, workDays)) {\n    nextDay.setDate(nextDay.getDate() + 1);\n  }\n  \n  return nextDay;\n}\n\n// ============================================================================\n// 相对时间\n// ============================================================================\n\n/**\n * 获取相对时间描述\n */\nexport function getRelativeTime(date: Date): string {\n  const now = new Date();\n  const diffMs = date.getTime() - now.getTime();\n  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));\n  \n  if (diffDays === 0) {\n    return '今天';\n  } else if (diffDays === 1) {\n    return '明天';\n  } else if (diffDays === -1) {\n    return '昨天';\n  } else if (diffDays > 1 && diffDays <= 7) {\n    return `${diffDays}天后`;\n  } else if (diffDays < -1 && diffDays >= -7) {\n    return `${Math.abs(diffDays)}天前`;\n  } else {\n    return formatDate(date);\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AASO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,YAAY,GAAG,KAAK,CAAC,GAAG;AACtC;AAKO,SAAS,eAAe,IAAU;IACvC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,GAAG;AACrC;AAKO,SAAS,eAAe,OAAe;IAC5C,IAAI,UAAU,mIAAA,CAAA,iBAAc,CAAC,gBAAgB,EAAE;QAC7C,OAAO,GAAG,QAAQ,EAAE,CAAC;IACvB;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;IAClE,MAAM,mBAAmB,UAAU,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;IAElE,IAAI,qBAAqB,GAAG;QAC1B,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB;IAEA,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC;AAC1C;AASO,SAAS,cAAc,OAAa,IAAI,MAAM;IACnD,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,OAAO;AACT;AAKO,SAAS,YAAY,OAAa,IAAI,MAAM;IACjD,MAAM,MAAM,IAAI,KAAK;IACrB,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI;IACzB,OAAO;AACT;AAKO,SAAS,YAAY,OAAa,IAAI,MAAM;IACjD,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;IACtC,OAAO;AACT;AAKO,SAAS,eAAe,OAAa,IAAI,MAAM;IACpD,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,OAAO,MAAM,OAAO,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU;IACrE,MAAM,OAAO,CAAC;IACd,OAAO,cAAc;AACvB;AAKO,SAAS,aAAa,OAAa,IAAI,MAAM;IAClD,MAAM,MAAM,IAAI,KAAK,eAAe;IACpC,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;IAC5B,OAAO,YAAY;AACrB;AAKO,SAAS,UAAU,KAAW,EAAE,KAAW;IAChD,OAAO,WAAW,WAAW,WAAW;AAC1C;AAKO,SAAS,QAAQ,IAAU;IAChC,OAAO,UAAU,MAAM,IAAI;AAC7B;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,UAAU,MAAM;AACzB;AAKO,SAAS,kBAAkB,KAAW,EAAE,KAAW;IACxD,MAAM,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO;IAChD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,OAAO,EAAE;AAC/C;AASO,SAAS,eAAe,OAAe,EAAE,WAAiB,IAAI,MAAM;IACzE,MAAM,CAAC,OAAO,QAAQ,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,KAAK,QAAQ,CAAC,OAAO,SAAS,GAAG;IACjC,OAAO;AACT;AAKO,SAAS,eAAe,IAAU,EAAE,OAAe;IACxD,MAAM,CAAC,OAAO,QAAQ,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;IAChD,MAAM,SAAS,IAAI,KAAK;IACxB,OAAO,QAAQ,CAAC,OAAO,SAAS,GAAG;IACnC,OAAO;AACT;AAKO,SAAS;IACd,MAAM,WAAW;IACjB,SAAS,QAAQ,CAAC,IAAI,GAAG,GAAG;IAC5B,OAAO;AACT;AASO,SAAS,cACd,IAAU,EACV,SAAiB,EACjB,OAAe,EACf,WAAiB,IAAI,MAAM;IAE3B,MAAM,QAAQ,eAAe,WAAW;IACxC,MAAM,MAAM,eAAe,SAAS;IAEpC,OAAO,QAAQ,SAAS,QAAQ;AAClC;AAKO,SAAS,mBACd,MAAY,EACZ,IAAU,EACV,MAAY,EACZ,IAAU;IAEV,OAAO,SAAS,QAAQ,OAAO;AACjC;AAKO,SAAS,kBAAkB,KAAW,EAAE,GAAS;IACtD,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;AAClE;AASO,SAAS,UAAU,IAAU,EAAE,WAAqB;IAAC;IAAU;IAAW;IAAa;IAAY;CAAS;IACjH,MAAM,WAAW;QAAC;QAAU;QAAU;QAAW;QAAa;QAAY;QAAU;KAAW;IAC/F,MAAM,UAAU,QAAQ,CAAC,KAAK,MAAM,GAAG;IACvC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AAKO,SAAS,eAAe,OAAa,IAAI,MAAM,EAAE,WAAqB;IAAC;IAAU;IAAW;IAAa;IAAY;CAAS;IACnI,IAAI,UAAU,IAAI,KAAK;IACvB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;IAEpC,MAAO,CAAC,UAAU,SAAS,UAAW;QACpC,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;IACtC;IAEA,OAAO;AACT;AASO,SAAS,gBAAgB,IAAU;IACxC,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,KAAK,OAAO,KAAK,IAAI,OAAO;IAC3C,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;IAExD,IAAI,aAAa,GAAG;QAClB,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO;IACT,OAAO,IAAI,aAAa,CAAC,GAAG;QAC1B,OAAO;IACT,OAAO,IAAI,WAAW,KAAK,YAAY,GAAG;QACxC,OAAO,GAAG,SAAS,EAAE,CAAC;IACxB,OAAO,IAAI,WAAW,CAAC,KAAK,YAAY,CAAC,GAAG;QAC1C,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,EAAE,CAAC;IAClC,OAAO;QACL,OAAO,WAAW;IACpB;AACF", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/shared/utils/taskUtils.ts"], "sourcesContent": ["/**\n * 任务相关的工具函数\n */\n\nimport { Task, TaskCategory, Priority, Quadrant, TaskStatus } from '../types/core';\nimport { QUADRANTS, TASK_CATEGORIES, PRIORITY_LEVELS } from '../constants';\n\n// ============================================================================\n// 四象限分类\n// ============================================================================\n\n/**\n * 根据重要性和紧急性确定四象限\n */\nexport function classifyQuadrant(importance: Priority, urgency: Priority): Quadrant {\n  if (importance >= 4 && urgency >= 4) {\n    return QUADRANTS.URGENT_IMPORTANT;\n  } else if (importance >= 4 && urgency < 4) {\n    return QUADRANTS.IMPORTANT_NOT_URGENT;\n  } else if (importance < 4 && urgency >= 4) {\n    return QUADRANTS.URGENT_NOT_IMPORTANT;\n  } else {\n    return QUADRANTS.NOT_URGENT_NOT_IMPORTANT;\n  }\n}\n\n/**\n * 获取四象限的描述\n */\nexport function getQuadrantDescription(quadrant: Quadrant): string {\n  const descriptions = {\n    [QUADRANTS.URGENT_IMPORTANT]: '重要且紧急 - 立即执行',\n    [QUADRANTS.IMPORTANT_NOT_URGENT]: '重要不紧急 - 计划执行',\n    [QUADRANTS.URGENT_NOT_IMPORTANT]: '不重要但紧急 - 委托处理',\n    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: '不重要不紧急 - 减少或删除'\n  };\n  return descriptions[quadrant];\n}\n\n/**\n * 获取四象限的简短标题\n */\nexport function getQuadrantTitle(quadrant: Quadrant): string {\n  const titles = {\n    [QUADRANTS.URGENT_IMPORTANT]: '象限 I',\n    [QUADRANTS.IMPORTANT_NOT_URGENT]: '象限 II', \n    [QUADRANTS.URGENT_NOT_IMPORTANT]: '象限 III',\n    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: '象限 IV'\n  };\n  return titles[quadrant];\n}\n\n// ============================================================================\n// 任务分类\n// ============================================================================\n\n/**\n * 获取任务分类的中文名称\n */\nexport function getCategoryName(category: TaskCategory): string {\n  const names = {\n    [TASK_CATEGORIES.WORK]: '工作',\n    [TASK_CATEGORIES.IMPROVEMENT]: '提升',\n    [TASK_CATEGORIES.ENTERTAINMENT]: '娱乐'\n  };\n  return names[category];\n}\n\n/**\n * 获取任务分类的颜色主题\n */\nexport function getCategoryColor(category: TaskCategory): string {\n  const colors = {\n    [TASK_CATEGORIES.WORK]: 'blue',\n    [TASK_CATEGORIES.IMPROVEMENT]: 'green',\n    [TASK_CATEGORIES.ENTERTAINMENT]: 'purple'\n  };\n  return colors[category];\n}\n\n/**\n * 获取任务分类的图标\n */\nexport function getCategoryIcon(category: TaskCategory): string {\n  const icons = {\n    [TASK_CATEGORIES.WORK]: '💼',\n    [TASK_CATEGORIES.IMPROVEMENT]: '📚',\n    [TASK_CATEGORIES.ENTERTAINMENT]: '🎮'\n  };\n  return icons[category];\n}\n\n// ============================================================================\n// 任务状态\n// ============================================================================\n\n/**\n * 获取任务状态的中文名称\n */\nexport function getStatusName(status: TaskStatus): string {\n  const names = {\n    pending: '待处理',\n    'in-progress': '进行中',\n    completed: '已完成',\n    postponed: '已推迟'\n  };\n  return names[status];\n}\n\n/**\n * 获取任务状态的颜色\n */\nexport function getStatusColor(status: TaskStatus): string {\n  const colors = {\n    pending: 'gray',\n    'in-progress': 'blue',\n    completed: 'green',\n    postponed: 'yellow'\n  };\n  return colors[status];\n}\n\n/**\n * 检查任务是否可以开始\n */\nexport function canStartTask(task: Task): boolean {\n  return task.status === 'pending' || task.status === 'postponed';\n}\n\n/**\n * 检查任务是否已完成\n */\nexport function isTaskCompleted(task: Task): boolean {\n  return task.status === 'completed';\n}\n\n/**\n * 检查任务是否进行中\n */\nexport function isTaskInProgress(task: Task): boolean {\n  return task.status === 'in-progress';\n}\n\n// ============================================================================\n// 优先级\n// ============================================================================\n\n/**\n * 获取优先级的中文名称\n */\nexport function getPriorityName(priority: Priority): string {\n  const names = {\n    [PRIORITY_LEVELS.VERY_LOW]: '很低',\n    [PRIORITY_LEVELS.LOW]: '较低',\n    [PRIORITY_LEVELS.MEDIUM]: '中等',\n    [PRIORITY_LEVELS.HIGH]: '较高',\n    [PRIORITY_LEVELS.VERY_HIGH]: '很高'\n  };\n  return names[priority];\n}\n\n/**\n * 获取优先级的颜色\n */\nexport function getPriorityColor(priority: Priority): string {\n  const colors = {\n    [PRIORITY_LEVELS.VERY_LOW]: 'gray',\n    [PRIORITY_LEVELS.LOW]: 'blue',\n    [PRIORITY_LEVELS.MEDIUM]: 'yellow',\n    [PRIORITY_LEVELS.HIGH]: 'orange',\n    [PRIORITY_LEVELS.VERY_HIGH]: 'red'\n  };\n  return colors[priority];\n}\n\n// ============================================================================\n// 任务过滤和排序\n// ============================================================================\n\n/**\n * 按分类过滤任务\n */\nexport function filterTasksByCategory(tasks: Task[], category: TaskCategory): Task[] {\n  return tasks.filter(task => task.category === category);\n}\n\n/**\n * 按状态过滤任务\n */\nexport function filterTasksByStatus(tasks: Task[], status: TaskStatus): Task[] {\n  return tasks.filter(task => task.status === status);\n}\n\n/**\n * 按四象限过滤任务\n */\nexport function filterTasksByQuadrant(tasks: Task[], quadrant: Quadrant): Task[] {\n  return tasks.filter(task => classifyQuadrant(task.importance, task.urgency) === quadrant);\n}\n\n/**\n * 获取今日需要处理的任务\n */\nexport function getTodayTasks(tasks: Task[]): Task[] {\n  const today = new Date();\n  const tomorrow = new Date(today);\n  tomorrow.setDate(tomorrow.getDate() + 1);\n  \n  return tasks.filter(task => {\n    // 包含今日截止的任务和未完成的高优先级任务\n    const isToday = task.deadline <= tomorrow;\n    const isHighPriority = task.importance >= 4 || task.urgency >= 4;\n    const isPending = task.status === 'pending' || task.status === 'in-progress';\n    \n    return isPending && (isToday || isHighPriority);\n  });\n}\n\n/**\n * 按优先级排序任务\n */\nexport function sortTasksByPriority(tasks: Task[]): Task[] {\n  return [...tasks].sort((a, b) => {\n    const aQuadrant = classifyQuadrant(a.importance, a.urgency);\n    const bQuadrant = classifyQuadrant(b.importance, b.urgency);\n    \n    // 先按象限排序\n    if (aQuadrant !== bQuadrant) {\n      return aQuadrant - bQuadrant;\n    }\n    \n    // 再按重要性排序\n    if (a.importance !== b.importance) {\n      return b.importance - a.importance;\n    }\n    \n    // 最后按紧急性排序\n    return b.urgency - a.urgency;\n  });\n}\n\n/**\n * 按截止时间排序任务\n */\nexport function sortTasksByDeadline(tasks: Task[]): Task[] {\n  return [...tasks].sort((a, b) => a.deadline.getTime() - b.deadline.getTime());\n}\n\n// ============================================================================\n// 任务验证\n// ============================================================================\n\n/**\n * 验证任务数据的完整性\n */\nexport function validateTask(task: Partial<Task>): string[] {\n  const errors: string[] = [];\n  \n  if (!task.title || task.title.trim().length === 0) {\n    errors.push('任务标题不能为空');\n  }\n  \n  if (task.title && task.title.length > 100) {\n    errors.push('任务标题不能超过100个字符');\n  }\n  \n  if (!task.category || !Object.values(TASK_CATEGORIES).includes(task.category as TaskCategory)) {\n    errors.push('请选择有效的任务分类');\n  }\n  \n  if (!task.importance || task.importance < 1 || task.importance > 5) {\n    errors.push('重要性必须在1-5之间');\n  }\n  \n  if (!task.urgency || task.urgency < 1 || task.urgency > 5) {\n    errors.push('紧急性必须在1-5之间');\n  }\n  \n  if (!task.deadline) {\n    errors.push('请设置截止时间');\n  } else if (task.deadline < new Date()) {\n    errors.push('截止时间不能早于当前时间');\n  }\n  \n  if (!task.estimatedDuration || task.estimatedDuration < 15 || task.estimatedDuration > 480) {\n    errors.push('预估时长必须在15分钟到8小时之间');\n  }\n  \n  return errors;\n}\n\n/**\n * 检查任务是否即将到期\n */\nexport function isTaskDueSoon(task: Task, hoursThreshold: number = 24): boolean {\n  const now = new Date();\n  const timeDiff = task.deadline.getTime() - now.getTime();\n  const hoursDiff = timeDiff / (1000 * 60 * 60);\n  \n  return hoursDiff > 0 && hoursDiff <= hoursThreshold;\n}\n\n/**\n * 检查任务是否已过期\n */\nexport function isTaskOverdue(task: Task): boolean {\n  return task.deadline < new Date() && task.status !== 'completed';\n}\n\n/**\n * 获取任务的紧急程度描述\n */\nexport function getTaskUrgencyDescription(task: Task): string {\n  if (isTaskOverdue(task)) {\n    return '已过期';\n  } else if (isTaskDueSoon(task, 2)) {\n    return '2小时内到期';\n  } else if (isTaskDueSoon(task, 24)) {\n    return '今日到期';\n  } else if (isTaskDueSoon(task, 48)) {\n    return '明日到期';\n  } else {\n    return '充足时间';\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;;AASO,SAAS,iBAAiB,UAAoB,EAAE,OAAiB;IACtE,IAAI,cAAc,KAAK,WAAW,GAAG;QACnC,OAAO,mIAAA,CAAA,YAAS,CAAC,gBAAgB;IACnC,OAAO,IAAI,cAAc,KAAK,UAAU,GAAG;QACzC,OAAO,mIAAA,CAAA,YAAS,CAAC,oBAAoB;IACvC,OAAO,IAAI,aAAa,KAAK,WAAW,GAAG;QACzC,OAAO,mIAAA,CAAA,YAAS,CAAC,oBAAoB;IACvC,OAAO;QACL,OAAO,mIAAA,CAAA,YAAS,CAAC,wBAAwB;IAC3C;AACF;AAKO,SAAS,uBAAuB,QAAkB;IACvD,MAAM,eAAe;QACnB,CAAC,mIAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,EAAE;QAC9B,CAAC,mIAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,EAAE;QAClC,CAAC,mIAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,EAAE;QAClC,CAAC,mIAAA,CAAA,YAAS,CAAC,wBAAwB,CAAC,EAAE;IACxC;IACA,OAAO,YAAY,CAAC,SAAS;AAC/B;AAKO,SAAS,iBAAiB,QAAkB;IACjD,MAAM,SAAS;QACb,CAAC,mIAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,EAAE;QAC9B,CAAC,mIAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,EAAE;QAClC,CAAC,mIAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,EAAE;QAClC,CAAC,mIAAA,CAAA,YAAS,CAAC,wBAAwB,CAAC,EAAE;IACxC;IACA,OAAO,MAAM,CAAC,SAAS;AACzB;AASO,SAAS,gBAAgB,QAAsB;IACpD,MAAM,QAAQ;QACZ,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;QACxB,CAAC,mIAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,EAAE;QAC/B,CAAC,mIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE;IACnC;IACA,OAAO,KAAK,CAAC,SAAS;AACxB;AAKO,SAAS,iBAAiB,QAAsB;IACrD,MAAM,SAAS;QACb,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;QACxB,CAAC,mIAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,EAAE;QAC/B,CAAC,mIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE;IACnC;IACA,OAAO,MAAM,CAAC,SAAS;AACzB;AAKO,SAAS,gBAAgB,QAAsB;IACpD,MAAM,QAAQ;QACZ,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;QACxB,CAAC,mIAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,EAAE;QAC/B,CAAC,mIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE;IACnC;IACA,OAAO,KAAK,CAAC,SAAS;AACxB;AASO,SAAS,cAAc,MAAkB;IAC9C,MAAM,QAAQ;QACZ,SAAS;QACT,eAAe;QACf,WAAW;QACX,WAAW;IACb;IACA,OAAO,KAAK,CAAC,OAAO;AACtB;AAKO,SAAS,eAAe,MAAkB;IAC/C,MAAM,SAAS;QACb,SAAS;QACT,eAAe;QACf,WAAW;QACX,WAAW;IACb;IACA,OAAO,MAAM,CAAC,OAAO;AACvB;AAKO,SAAS,aAAa,IAAU;IACrC,OAAO,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;AACtD;AAKO,SAAS,gBAAgB,IAAU;IACxC,OAAO,KAAK,MAAM,KAAK;AACzB;AAKO,SAAS,iBAAiB,IAAU;IACzC,OAAO,KAAK,MAAM,KAAK;AACzB;AASO,SAAS,gBAAgB,QAAkB;IAChD,MAAM,QAAQ;QACZ,CAAC,mIAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,EAAE;QAC5B,CAAC,mIAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,EAAE;QACvB,CAAC,mIAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,EAAE;QAC1B,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;QACxB,CAAC,mIAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,EAAE;IAC/B;IACA,OAAO,KAAK,CAAC,SAAS;AACxB;AAKO,SAAS,iBAAiB,QAAkB;IACjD,MAAM,SAAS;QACb,CAAC,mIAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,EAAE;QAC5B,CAAC,mIAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,EAAE;QACvB,CAAC,mIAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,EAAE;QAC1B,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;QACxB,CAAC,mIAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,EAAE;IAC/B;IACA,OAAO,MAAM,CAAC,SAAS;AACzB;AASO,SAAS,sBAAsB,KAAa,EAAE,QAAsB;IACzE,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAChD;AAKO,SAAS,oBAAoB,KAAa,EAAE,MAAkB;IACnE,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AAC9C;AAKO,SAAS,sBAAsB,KAAa,EAAE,QAAkB;IACrE,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,iBAAiB,KAAK,UAAU,EAAE,KAAK,OAAO,MAAM;AAClF;AAKO,SAAS,cAAc,KAAa;IACzC,MAAM,QAAQ,IAAI;IAClB,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;IAEtC,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,uBAAuB;QACvB,MAAM,UAAU,KAAK,QAAQ,IAAI;QACjC,MAAM,iBAAiB,KAAK,UAAU,IAAI,KAAK,KAAK,OAAO,IAAI;QAC/D,MAAM,YAAY,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;QAE/D,OAAO,aAAa,CAAC,WAAW,cAAc;IAChD;AACF;AAKO,SAAS,oBAAoB,KAAa;IAC/C,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,YAAY,iBAAiB,EAAE,UAAU,EAAE,EAAE,OAAO;QAC1D,MAAM,YAAY,iBAAiB,EAAE,UAAU,EAAE,EAAE,OAAO;QAE1D,SAAS;QACT,IAAI,cAAc,WAAW;YAC3B,OAAO,YAAY;QACrB;QAEA,UAAU;QACV,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;YACjC,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU;QACpC;QAEA,WAAW;QACX,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;IAC9B;AACF;AAKO,SAAS,oBAAoB,KAAa;IAC/C,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO;AAC5E;AASO,SAAS,aAAa,IAAmB;IAC9C,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QACjD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK;QACzC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,mIAAA,CAAA,kBAAe,EAAE,QAAQ,CAAC,KAAK,QAAQ,GAAmB;QAC7F,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,KAAK,KAAK,UAAU,GAAG,GAAG;QAClE,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,GAAG;QACzD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;QAClB,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,QAAQ;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,GAAG,MAAM,KAAK,iBAAiB,GAAG,KAAK;QAC1F,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAKO,SAAS,cAAc,IAAU,EAAE,iBAAyB,EAAE;IACnE,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,KAAK,QAAQ,CAAC,OAAO,KAAK,IAAI,OAAO;IACtD,MAAM,YAAY,WAAW,CAAC,OAAO,KAAK,EAAE;IAE5C,OAAO,YAAY,KAAK,aAAa;AACvC;AAKO,SAAS,cAAc,IAAU;IACtC,OAAO,KAAK,QAAQ,GAAG,IAAI,UAAU,KAAK,MAAM,KAAK;AACvD;AAKO,SAAS,0BAA0B,IAAU;IAClD,IAAI,cAAc,OAAO;QACvB,OAAO;IACT,OAAO,IAAI,cAAc,MAAM,IAAI;QACjC,OAAO;IACT,OAAO,IAAI,cAAc,MAAM,KAAK;QAClC,OAAO;IACT,OAAO,IAAI,cAAc,MAAM,KAAK;QAClC,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/shared/index.ts"], "sourcesContent": ["/**\n * 共享模块的统一导出\n * 提供类型、常量、工具函数的统一入口\n */\n\n// ============================================================================\n// 类型导出\n// ============================================================================\n\nexport type {\n  // 核心业务类型\n  Task,\n  TaskCategory,\n  Priority,\n  TaskStatus,\n  Quadrant,\n  \n  // 时间相关类型\n  TimeSlot,\n  DailySchedule,\n  ScoredTask,\n  \n  // 用户配置类型\n  UserProfile,\n  WorkHours,\n  UserTimeConfig,\n  FixedTimeSlot,\n  CategoryPreferences,\n  CategoryPreference,\n  \n  // 分析相关类型\n  CategoryRatios,\n  BalanceAnalysis,\n  WeeklyStats,\n  DailyStats,\n  \n  // 算法相关类型\n  PostponedTaskAlert,\n  AdjustmentResult,\n  \n  // API 类型\n  ApiResponse,\n  PaginatedResponse,\n  \n  // 事件类型\n  CalendarEvent,\n  \n  // 错误类型\n  AppError\n} from './types/core';\n\n// ============================================================================\n// 常量导出\n// ============================================================================\n\nexport {\n  // 任务相关常量\n  TASK_CATEGORIES,\n  TASK_STATUSES,\n  PRIORITY_LEVELS,\n  QUADRANTS,\n  \n  // 时间相关常量\n  TIME_CONSTANTS,\n  WORK_DAYS,\n  ALL_DAYS,\n  DAY_NAMES_CN,\n  \n  // 默认配置\n  DEFAULT_USER_CONFIG,\n  \n  // 算法常量\n  ALGORITHM_CONSTANTS,\n  \n  // UI 常量\n  UI_CONSTANTS,\n  \n  // 错误代码\n  ERROR_CODES\n} from './constants';\n\n// ============================================================================\n// 工具函数导出\n// ============================================================================\n\nexport {\n  // 日期工具\n  formatDate,\n  formatTime,\n  formatDateTime,\n  formatDuration,\n  getStartOfDay,\n  getEndOfDay,\n  getTomorrow,\n  getStartOfWeek,\n  getEndOfWeek,\n  isSameDay,\n  isToday,\n  isTomorrow,\n  getDaysDifference,\n  parseTimeToday,\n  createDateTime,\n  getDefaultDeadline,\n  isTimeInRange,\n  isTimeRangeOverlap,\n  getMinutesBetween,\n  isWorkday,\n  getNextWorkday,\n  getRelativeTime\n} from './utils/dateUtils';\n\nexport {\n  // 任务工具\n  classifyQuadrant,\n  getQuadrantDescription,\n  getQuadrantTitle,\n  getCategoryName,\n  getCategoryColor,\n  getCategoryIcon,\n  getStatusName,\n  getStatusColor,\n  canStartTask,\n  isTaskCompleted,\n  isTaskInProgress,\n  getPriorityName,\n  getPriorityColor,\n  filterTasksByCategory,\n  filterTasksByStatus,\n  filterTasksByQuadrant,\n  getTodayTasks,\n  sortTasksByPriority,\n  sortTasksByDeadline,\n  validateTask,\n  isTaskDueSoon,\n  isTaskOverdue,\n  getTaskUrgencyDescription\n} from './utils/taskUtils';\n\n// ============================================================================\n// 类型守卫和验证函数\n// ============================================================================\n\n/**\n * 检查是否为有效的任务分类\n */\nexport function isValidTaskCategory(value: any): value is TaskCategory {\n  return Object.values(TASK_CATEGORIES).includes(value);\n}\n\n/**\n * 检查是否为有效的优先级\n */\nexport function isValidPriority(value: any): value is Priority {\n  return typeof value === 'number' && value >= 1 && value <= 5;\n}\n\n/**\n * 检查是否为有效的任务状态\n */\nexport function isValidTaskStatus(value: any): value is TaskStatus {\n  return Object.values(TASK_STATUSES).includes(value);\n}\n\n/**\n * 检查是否为有效的四象限\n */\nexport function isValidQuadrant(value: any): value is Quadrant {\n  return typeof value === 'number' && value >= 1 && value <= 4;\n}\n\n// ============================================================================\n// 错误处理工具\n// ============================================================================\n\n/**\n * 创建应用错误对象\n */\nexport function createAppError(\n  code: string,\n  message: string,\n  details?: any\n): AppError {\n  return {\n    code,\n    message,\n    details,\n    timestamp: new Date()\n  };\n}\n\n/**\n * 检查是否为应用错误\n */\nexport function isAppError(error: any): error is AppError {\n  return error && \n         typeof error.code === 'string' && \n         typeof error.message === 'string' && \n         error.timestamp instanceof Date;\n}\n\n// ============================================================================\n// 数据转换工具\n// ============================================================================\n\n/**\n * 将小时转换为分钟\n */\nexport function hoursToMinutes(hours: number): number {\n  return Math.round(hours * TIME_CONSTANTS.MINUTES_PER_HOUR);\n}\n\n/**\n * 将分钟转换为小时\n */\nexport function minutesToHours(minutes: number): number {\n  return minutes / TIME_CONSTANTS.MINUTES_PER_HOUR;\n}\n\n/**\n * 安全地解析 JSON\n */\nexport function safeJsonParse<T>(json: string, defaultValue: T): T {\n  try {\n    return JSON.parse(json);\n  } catch {\n    return defaultValue;\n  }\n}\n\n/**\n * 深度克隆对象\n */\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') {\n    return obj;\n  }\n  \n  if (obj instanceof Date) {\n    return new Date(obj.getTime()) as unknown as T;\n  }\n  \n  if (Array.isArray(obj)) {\n    return obj.map(item => deepClone(item)) as unknown as T;\n  }\n  \n  const cloned = {} as T;\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      cloned[key] = deepClone(obj[key]);\n    }\n  }\n  \n  return cloned;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;;;;;;;;;;;;;AA4C/E,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E;AA0BA,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;AA0BA;;;;AAkCO,SAAS,oBAAoB,KAAU;IAC5C,OAAO,OAAO,MAAM,CAAC,iBAAiB,QAAQ,CAAC;AACjD;AAKO,SAAS,gBAAgB,KAAU;IACxC,OAAO,OAAO,UAAU,YAAY,SAAS,KAAK,SAAS;AAC7D;AAKO,SAAS,kBAAkB,KAAU;IAC1C,OAAO,OAAO,MAAM,CAAC,eAAe,QAAQ,CAAC;AAC/C;AAKO,SAAS,gBAAgB,KAAU;IACxC,OAAO,OAAO,UAAU,YAAY,SAAS,KAAK,SAAS;AAC7D;AASO,SAAS,eACd,IAAY,EACZ,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA;QACA;QACA,WAAW,IAAI;IACjB;AACF;AAKO,SAAS,WAAW,KAAU;IACnC,OAAO,SACA,OAAO,MAAM,IAAI,KAAK,YACtB,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,SAAS,YAAY;AACpC;AASO,SAAS,eAAe,KAAa;IAC1C,OAAO,KAAK,KAAK,CAAC,QAAQ,eAAe,gBAAgB;AAC3D;AAKO,SAAS,eAAe,OAAe;IAC5C,OAAO,UAAU,eAAe,gBAAgB;AAClD;AAKO,SAAS,cAAiB,IAAY,EAAE,YAAe;IAC5D,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QAC3C,OAAO;IACT;IAEA,IAAI,eAAe,MAAM;QACvB,OAAO,IAAI,KAAK,IAAI,OAAO;IAC7B;IAEA,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IACnC;IAEA,MAAM,SAAS,CAAC;IAChB,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,IAAI,cAAc,CAAC,MAAM;YAC3B,MAAM,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;QAClC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts"], "sourcesContent": ["/**\n * 时间规划算法\n * 负责根据任务优先级和用户时间配置生成智能的时间安排\n */\n\nimport {\n  Task,\n  ScoredTask,\n  DailySchedule,\n  TimeSlot,\n  UserTimeConfig,\n  Priority,\n  Quadrant,\n  TaskCategory,\n  DEFAULT_USER_CONFIG,\n  ALGORITHM_CONSTANTS,\n  QUADRANTS,\n  classifyQuadrant,\n  getTodayTasks,\n  isTimeRangeOverlap\n} from '@/shared';\n\nexport class PlanningAlgorithm {\n  \n  /**\n   * 生成今日时间安排\n   */\n  generateDailySchedule(tasks: Task[], userTimeConfig?: UserTimeConfig): DailySchedule {\n    // 1. 过滤今日需要处理的任务\n    const todayTasks = this.filterTodayTasks(tasks);\n\n    // 2. 计算分数并分类\n    const scoredTasks: ScoredTask[] = todayTasks\n      .map(task => ({\n        ...task,\n        score: this.calculateTaskScore(task),\n        quadrant: classifyQuadrant(task.importance, task.urgency)\n      }))\n      .sort((a, b) => {\n        // 先按象限排序，再按分数排序\n        if (a.quadrant !== b.quadrant) {\n          return a.quadrant - b.quadrant;\n        }\n        return b.score - a.score;\n      });\n\n    // 3. 生成时间段（使用新的基于任务类型的算法）\n    const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);\n\n    // 4. 计算总时长\n    const totalDuration = timeSlots.reduce((sum, slot) =>\n      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0\n    );\n\n    return {\n      date: new Date(),\n      timeSlots,\n      totalTasks: todayTasks.length,\n      estimatedDuration: totalDuration\n    };\n  }\n\n  /**\n   * 过滤今日需要处理的任务\n   */\n  private filterTodayTasks(tasks: Task[]): Task[] {\n    return getTodayTasks(tasks);\n  }\n\n  /**\n   * 计算任务综合分数\n   */\n  private calculateTaskScore(task: Task): number {\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n    \n    // 基础分数：象限权重 * (重要性 + 紧急性)\n    const baseScore = ALGORITHM_CONSTANTS.QUADRANT_WEIGHTS[quadrant] * \n                     (task.importance + task.urgency);\n    \n    // 截止日期权重\n    const now = new Date();\n    const timeToDeadline = task.deadline.getTime() - now.getTime();\n    const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);\n    \n    let deadlineWeight = 1;\n    if (hoursToDeadline < 2) {\n      deadlineWeight = 2.0; // 2小时内，权重翻倍\n    } else if (hoursToDeadline < 24) {\n      deadlineWeight = 1.5; // 24小时内，权重增加50%\n    } else if (hoursToDeadline < 48) {\n      deadlineWeight = 1.2; // 48小时内，权重增加20%\n    }\n    \n    // 推迟惩罚\n    const postponePenalty = task.postponeCount * ALGORITHM_CONSTANTS.POSTPONE_PENALTY;\n    \n    // 分类权重\n    const categoryWeight = ALGORITHM_CONSTANTS.CATEGORY_WEIGHTS[task.category];\n    \n    return baseScore * deadlineWeight * categoryWeight * (1 + postponePenalty);\n  }\n\n  /**\n   * 基于任务类型生成时间段安排\n   */\n  private generateTimeSlotsWithCategories(tasks: ScoredTask[], userTimeConfig?: UserTimeConfig): TimeSlot[] {\n    const timeSlots: TimeSlot[] = [];\n    const today = new Date();\n\n    // 使用默认配置如果没有提供用户配置\n    const config = userTimeConfig || DEFAULT_USER_CONFIG;\n\n    // 按任务类型分组\n    const tasksByCategory = this.groupTasksByCategory(tasks);\n\n    // 为每种类型的任务安排时间\n    for (const [category, categoryTasks] of Object.entries(tasksByCategory)) {\n      if (categoryTasks.length === 0) continue;\n\n      const categoryPrefs = config.categoryPreferences[category as TaskCategory];\n      if (!categoryPrefs) continue;\n\n      // 为该类型任务生成可用时间段\n      const availableSlots = this.generateAvailableSlots(\n        categoryPrefs.preferredTimes, \n        config.fixedSlots, \n        today\n      );\n\n      // 在可用时间段中安排任务\n      this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);\n    }\n\n    return timeSlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());\n  }\n\n  /**\n   * 按任务类型分组\n   */\n  private groupTasksByCategory(tasks: ScoredTask[]): Record<TaskCategory, ScoredTask[]> {\n    return {\n      work: tasks.filter(task => task.category === 'work'),\n      improvement: tasks.filter(task => task.category === 'improvement'),\n      entertainment: tasks.filter(task => task.category === 'entertainment')\n    };\n  }\n\n  /**\n   * 生成可用时间段\n   */\n  private generateAvailableSlots(\n    preferredTimes: string[], \n    fixedSlots: any[], \n    date: Date\n  ): { start: Date; end: Date }[] {\n    const availableSlots: { start: Date; end: Date }[] = [];\n\n    for (const timeRange of preferredTimes) {\n      const [startTime, endTime] = timeRange.split('-');\n      const [startHour, startMinute] = startTime.split(':').map(Number);\n      const [endHour, endMinute] = endTime.split(':').map(Number);\n\n      const slotStart = new Date(date);\n      slotStart.setHours(startHour, startMinute, 0, 0);\n\n      const slotEnd = new Date(date);\n      slotEnd.setHours(endHour, endMinute, 0, 0);\n\n      // 检查是否与固定时间段冲突\n      let hasConflict = false;\n      for (const fixedSlot of fixedSlots) {\n        const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);\n        const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);\n\n        const fixedStart = new Date(date);\n        fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);\n\n        const fixedEnd = new Date(date);\n        fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);\n\n        // 检查时间段重叠\n        if (isTimeRangeOverlap(slotStart, slotEnd, fixedStart, fixedEnd)) {\n          hasConflict = true;\n          break;\n        }\n      }\n\n      if (!hasConflict) {\n        availableSlots.push({ start: slotStart, end: slotEnd });\n      }\n    }\n\n    return availableSlots;\n  }\n\n  /**\n   * 在可用时间段中安排任务\n   */\n  private scheduleTasksInSlots(\n    tasks: ScoredTask[], \n    availableSlots: { start: Date; end: Date }[], \n    timeSlots: TimeSlot[]\n  ): void {\n    let currentSlotIndex = 0;\n    let currentTime = availableSlots[0]?.start;\n\n    if (!currentTime) return;\n\n    for (const task of tasks) {\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\n\n      // 寻找合适的时间段\n      while (currentSlotIndex < availableSlots.length) {\n        const currentSlot = availableSlots[currentSlotIndex];\n        const remainingTime = currentSlot.end.getTime() - currentTime.getTime();\n\n        if (remainingTime >= taskDuration) {\n          // 在当前时间段安排任务\n          const endTime = new Date(currentTime.getTime() + taskDuration);\n\n          timeSlots.push({\n            task,\n            startTime: new Date(currentTime),\n            endTime,\n            isFixed: false\n          });\n\n          // 更新当前时间，添加15分钟休息时间\n          currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\n          break;\n        } else {\n          // 移动到下一个时间段\n          currentSlotIndex++;\n          currentTime = availableSlots[currentSlotIndex]?.start;\n          if (!currentTime) break;\n        }\n      }\n    }\n  }\n\n  /**\n   * 获取任务建议\n   */\n  getTaskRecommendation(task: Task): string {\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n\n    if (quadrant === QUADRANTS.URGENT_IMPORTANT) {\n      return '🔥 高优先级任务，建议立即处理';\n    } else if (quadrant === QUADRANTS.IMPORTANT_NOT_URGENT) {\n      return '📅 重要任务，建议合理安排时间';\n    } else if (quadrant === QUADRANTS.URGENT_NOT_IMPORTANT) {\n      return '⚡ 紧急但不重要，考虑委托或快速处理';\n    } else {\n      return '🤔 优先级较低，可以延后或删除';\n    }\n  }\n\n  /**\n   * 生成时间段安排（保留原方法作为后备）\n   */\n  generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {\n    const timeSlots: TimeSlot[] = [];\n    const today = new Date();\n\n    // 解析工作时间\n    const [startHour, startMinute] = workHours.start.split(':').map(Number);\n    const [endHour, endMinute] = workHours.end.split(':').map(Number);\n\n    let currentTime = new Date(today);\n    currentTime.setHours(startHour, startMinute, 0, 0);\n\n    const workEndTime = new Date(today);\n    workEndTime.setHours(endHour, endMinute, 0, 0);\n\n    for (const task of tasks) {\n      // 检查是否还有足够的工作时间\n      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\n\n      if (remainingWorkTime < taskDuration) {\n        // 如果当天时间不够，跳过或安排到明天\n        continue;\n      }\n\n      const endTime = new Date(currentTime.getTime() + taskDuration);\n\n      timeSlots.push({\n        task,\n        startTime: new Date(currentTime),\n        endTime,\n        isFixed: false\n      });\n\n      // 更新当前时间，添加15分钟休息时间\n      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\n\n      // 如果超过工作时间，停止安排\n      if (currentTime >= workEndTime) {\n        break;\n      }\n    }\n\n    return timeSlots;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AAAA;AAAA;;AAiBO,MAAM;IAEX;;GAEC,GACD,sBAAsB,KAAa,EAAE,cAA+B,EAAiB;QACnF,iBAAiB;QACjB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAEzC,aAAa;QACb,MAAM,cAA4B,WAC/B,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,OAAO,IAAI,CAAC,kBAAkB,CAAC;gBAC/B,UAAU,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;YAC1D,CAAC,GACA,IAAI,CAAC,CAAC,GAAG;YACR,gBAAgB;YAChB,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;QAEF,0BAA0B;QAC1B,MAAM,YAAY,IAAI,CAAC,+BAA+B,CAAC,aAAa;QAEpE,WAAW;QACX,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC,KAAK,OAC3C,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG;QAG3E,OAAO;YACL,MAAM,IAAI;YACV;YACA,YAAY,WAAW,MAAM;YAC7B,mBAAmB;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,KAAa,EAAU;QAC9C,OAAO,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;IACvB;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAU;QAC7C,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAE/D,0BAA0B;QAC1B,MAAM,YAAY,mIAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC,SAAS,GAC/C,CAAC,KAAK,UAAU,GAAG,KAAK,OAAO;QAEhD,SAAS;QACT,MAAM,MAAM,IAAI;QAChB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,OAAO,KAAK,IAAI,OAAO;QAC5D,MAAM,kBAAkB,iBAAiB,CAAC,OAAO,KAAK,EAAE;QAExD,IAAI,iBAAiB;QACrB,IAAI,kBAAkB,GAAG;YACvB,iBAAiB,KAAK,YAAY;QACpC,OAAO,IAAI,kBAAkB,IAAI;YAC/B,iBAAiB,KAAK,gBAAgB;QACxC,OAAO,IAAI,kBAAkB,IAAI;YAC/B,iBAAiB,KAAK,gBAAgB;QACxC;QAEA,OAAO;QACP,MAAM,kBAAkB,KAAK,aAAa,GAAG,mIAAA,CAAA,sBAAmB,CAAC,gBAAgB;QAEjF,OAAO;QACP,MAAM,iBAAiB,mIAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC,KAAK,QAAQ,CAAC;QAE1E,OAAO,YAAY,iBAAiB,iBAAiB,CAAC,IAAI,eAAe;IAC3E;IAEA;;GAEC,GACD,AAAQ,gCAAgC,KAAmB,EAAE,cAA+B,EAAc;QACxG,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,mBAAmB;QACnB,MAAM,SAAS,kBAAkB,mIAAA,CAAA,sBAAmB;QAEpD,UAAU;QACV,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC;QAElD,eAAe;QACf,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,iBAAkB;YACvE,IAAI,cAAc,MAAM,KAAK,GAAG;YAEhC,MAAM,gBAAgB,OAAO,mBAAmB,CAAC,SAAyB;YAC1E,IAAI,CAAC,eAAe;YAEpB,gBAAgB;YAChB,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAChD,cAAc,cAAc,EAC5B,OAAO,UAAU,EACjB;YAGF,cAAc;YACd,IAAI,CAAC,oBAAoB,CAAC,eAAe,gBAAgB;QAC3D;QAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAC7E;IAEA;;GAEC,GACD,AAAQ,qBAAqB,KAAmB,EAAsC;QACpF,OAAO;YACL,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YAC7C,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YACpD,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACxD;IACF;IAEA;;GAEC,GACD,AAAQ,uBACN,cAAwB,EACxB,UAAiB,EACjB,IAAU,EACoB;QAC9B,MAAM,iBAA+C,EAAE;QAEvD,KAAK,MAAM,aAAa,eAAgB;YACtC,MAAM,CAAC,WAAW,QAAQ,GAAG,UAAU,KAAK,CAAC;YAC7C,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1D,MAAM,CAAC,SAAS,UAAU,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEpD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,WAAW,aAAa,GAAG;YAE9C,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,SAAS,WAAW,GAAG;YAExC,eAAe;YACf,IAAI,cAAc;YAClB,KAAK,MAAM,aAAa,WAAY;gBAClC,MAAM,CAAC,gBAAgB,iBAAiB,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAC1E,MAAM,CAAC,cAAc,eAAe,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAEpE,MAAM,aAAa,IAAI,KAAK;gBAC5B,WAAW,QAAQ,CAAC,gBAAgB,kBAAkB,GAAG;gBAEzD,MAAM,WAAW,IAAI,KAAK;gBAC1B,SAAS,QAAQ,CAAC,cAAc,gBAAgB,GAAG;gBAEnD,UAAU;gBACV,IAAI,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,SAAS,YAAY,WAAW;oBAChE,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,eAAe,IAAI,CAAC;oBAAE,OAAO;oBAAW,KAAK;gBAAQ;YACvD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBACN,KAAmB,EACnB,cAA4C,EAC5C,SAAqB,EACf;QACN,IAAI,mBAAmB;QACvB,IAAI,cAAc,cAAc,CAAC,EAAE,EAAE;QAErC,IAAI,CAAC,aAAa;QAElB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,WAAW;YACX,MAAO,mBAAmB,eAAe,MAAM,CAAE;gBAC/C,MAAM,cAAc,cAAc,CAAC,iBAAiB;gBACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC,OAAO,KAAK,YAAY,OAAO;gBAErE,IAAI,iBAAiB,cAAc;oBACjC,aAAa;oBACb,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;oBAEjD,UAAU,IAAI,CAAC;wBACb;wBACA,WAAW,IAAI,KAAK;wBACpB;wBACA,SAAS;oBACX;oBAEA,oBAAoB;oBACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;oBACrD;gBACF,OAAO;oBACL,YAAY;oBACZ;oBACA,cAAc,cAAc,CAAC,iBAAiB,EAAE;oBAChD,IAAI,CAAC,aAAa;gBACpB;YACF;QACF;IACF;IAEA;;GAEC,GACD,sBAAsB,IAAU,EAAU;QACxC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAE/D,IAAI,aAAa,mIAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;YAC3C,OAAO;QACT,OAAO,IAAI,aAAa,mIAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE;YACtD,OAAO;QACT,OAAO,IAAI,aAAa,mIAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE;YACtD,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA;;GAEC,GACD,kBAAkB,KAAmB,EAAE,SAAyC,EAAc;QAC5F,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,SAAS;QACT,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAChE,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAE1D,IAAI,cAAc,IAAI,KAAK;QAC3B,YAAY,QAAQ,CAAC,WAAW,aAAa,GAAG;QAEhD,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC,SAAS,WAAW,GAAG;QAE5C,KAAK,MAAM,QAAQ,MAAO;YACxB,gBAAgB;YAChB,MAAM,oBAAoB,YAAY,OAAO,KAAK,YAAY,OAAO;YACrE,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,IAAI,oBAAoB,cAAc;gBAEpC;YACF;YAEA,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;YAEjD,UAAU,IAAI,CAAC;gBACb;gBACA,WAAW,IAAI,KAAK;gBACpB;gBACA,SAAS;YACX;YAEA,oBAAoB;YACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;YAErD,gBAAgB;YAChB,IAAI,eAAe,aAAa;gBAC9B;YACF;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts"], "sourcesContent": ["/**\n * 生活平衡算法\n * 分析用户的时间分配，提供生活平衡建议\n */\n\nimport {\n  Task,\n  DailyStats,\n  BalanceAnalysis,\n  WeeklyStats,\n  CategoryRatios,\n  TaskCategory,\n  TASK_CATEGORIES,\n  getStartOfWeek,\n  getEndOfWeek,\n  formatDate\n} from '@/shared';\n\nexport class BalanceAlgorithm {\n\n  /**\n   * 分析用户的生活平衡状况\n   */\n  async analyzeBalance(userId: string, date: Date = new Date()): Promise<BalanceAnalysis> {\n    // 获取本周的统计数据\n    const weeklyStats = await this.calculateWeeklyStats(userId, date);\n    \n    // 计算分类比例\n    const categoryRatios = this.calculateCategoryRatios(weeklyStats.totalTime);\n    \n    // 计算平衡分数\n    const balanceScore = this.calculateBalanceScore(categoryRatios);\n    \n    // 生成建议\n    const recommendations = this.generateRecommendations(categoryRatios, weeklyStats);\n\n    return {\n      userId,\n      date,\n      categoryRatios,\n      weeklyStats,\n      recommendations,\n      balanceScore\n    };\n  }\n\n  /**\n   * 计算本周统计数据\n   */\n  private async calculateWeeklyStats(userId: string, date: Date): Promise<WeeklyStats> {\n    const startOfWeek = getStartOfWeek(date);\n    const endOfWeek = getEndOfWeek(date);\n    \n    // 这里应该从数据库获取数据，暂时使用模拟数据\n    const mockStats: CategoryRatios = {\n      work: 2400, // 40小时 * 60分钟\n      improvement: 420, // 7小时\n      entertainment: 600 // 10小时\n    };\n\n    const totalMinutes = mockStats.work + mockStats.improvement + mockStats.entertainment;\n    \n    const averageDaily: CategoryRatios = {\n      work: mockStats.work / 7,\n      improvement: mockStats.improvement / 7,\n      entertainment: mockStats.entertainment / 7\n    };\n\n    // 计算趋势（这里简化处理）\n    const trend = this.calculateTrend(mockStats);\n\n    return {\n      totalTime: mockStats,\n      averageDaily,\n      trend\n    };\n  }\n\n  /**\n   * 计算分类比例\n   */\n  private calculateCategoryRatios(totalTime: CategoryRatios): CategoryRatios {\n    const total = totalTime.work + totalTime.improvement + totalTime.entertainment;\n    \n    if (total === 0) {\n      return { work: 0, improvement: 0, entertainment: 0 };\n    }\n\n    return {\n      work: Math.round((totalTime.work / total) * 100) / 100,\n      improvement: Math.round((totalTime.improvement / total) * 100) / 100,\n      entertainment: Math.round((totalTime.entertainment / total) * 100) / 100\n    };\n  }\n\n  /**\n   * 计算平衡分数 (0-100)\n   */\n  private calculateBalanceScore(ratios: CategoryRatios): number {\n    // 理想比例：工作60%，提升25%，娱乐15%\n    const idealRatios = { work: 0.6, improvement: 0.25, entertainment: 0.15 };\n    \n    // 计算与理想比例的偏差\n    const workDeviation = Math.abs(ratios.work - idealRatios.work);\n    const improvementDeviation = Math.abs(ratios.improvement - idealRatios.improvement);\n    const entertainmentDeviation = Math.abs(ratios.entertainment - idealRatios.entertainment);\n    \n    // 总偏差\n    const totalDeviation = workDeviation + improvementDeviation + entertainmentDeviation;\n    \n    // 转换为分数 (偏差越小，分数越高)\n    const score = Math.max(0, 100 - (totalDeviation * 100));\n    \n    return Math.round(score);\n  }\n\n  /**\n   * 计算趋势\n   */\n  private calculateTrend(currentStats: CategoryRatios): 'improving' | 'declining' | 'stable' {\n    // 这里应该比较本周与上周的数据\n    // 暂时返回稳定状态\n    return 'stable';\n  }\n\n  /**\n   * 生成平衡建议\n   */\n  private generateRecommendations(ratios: CategoryRatios, weeklyStats: WeeklyStats): string[] {\n    const recommendations: string[] = [];\n\n    // 工作时间建议\n    if (ratios.work > 0.7) {\n      recommendations.push('工作时间占比过高，建议适当减少工作量，增加休息时间');\n    } else if (ratios.work < 0.5) {\n      recommendations.push('工作时间占比较低，可以考虑提高工作效率或增加工作时间');\n    }\n\n    // 提升时间建议\n    if (ratios.improvement < 0.15) {\n      recommendations.push('个人提升时间不足，建议每天安排至少1-2小时用于学习和成长');\n    } else if (ratios.improvement > 0.35) {\n      recommendations.push('个人提升时间充足，保持良好的学习习惯');\n    }\n\n    // 娱乐时间建议\n    if (ratios.entertainment < 0.1) {\n      recommendations.push('娱乐时间过少，适当的放松有助于提高工作效率');\n    } else if (ratios.entertainment > 0.25) {\n      recommendations.push('娱乐时间较多，可以考虑将部分时间用于工作或学习');\n    }\n\n    // 平衡性建议\n    const balanceScore = this.calculateBalanceScore(ratios);\n    if (balanceScore >= 80) {\n      recommendations.push('时间分配很均衡，继续保持！');\n    } else if (balanceScore >= 60) {\n      recommendations.push('时间分配基本合理，可以进行微调优化');\n    } else {\n      recommendations.push('时间分配需要调整，建议重新规划各类活动的时间比例');\n    }\n\n    return recommendations;\n  }\n\n  /**\n   * 更新今日统计\n   */\n  async updateTodayStats(\n    userId: string, \n    category: TaskCategory, \n    duration: number\n  ): Promise<void> {\n    const today = new Date();\n    const dateStr = formatDate(today);\n\n    // 这里应该更新数据库中的统计数据\n    console.log(`更新统计: 用户${userId}, 日期${dateStr}, 分类${category}, 时长${duration}分钟`);\n    \n    // 模拟数据库操作\n    // await supabase.from('daily_stats').upsert({\n    //   user_id: userId,\n    //   date: dateStr,\n    //   [category + '_time']: duration\n    // });\n  }\n\n  /**\n   * 获取分类时间建议\n   */\n  getCategoryTimeRecommendation(category: TaskCategory, currentRatio: number): string {\n    const recommendations = {\n      [TASK_CATEGORIES.WORK]: {\n        low: '工作时间不足，建议增加专注工作的时间',\n        normal: '工作时间合理，保持当前节奏',\n        high: '工作时间过长，注意劳逸结合'\n      },\n      [TASK_CATEGORIES.IMPROVEMENT]: {\n        low: '学习时间不足，建议每天安排固定的学习时间',\n        normal: '学习时间充足，继续保持学习习惯',\n        high: '学习时间很充足，可以考虑实践应用'\n      },\n      [TASK_CATEGORIES.ENTERTAINMENT]: {\n        low: '娱乐时间过少，适当放松有助于身心健康',\n        normal: '娱乐时间合适，保持工作生活平衡',\n        high: '娱乐时间较多，可以考虑更多有意义的活动'\n      }\n    };\n\n    let level: 'low' | 'normal' | 'high';\n    if (currentRatio < 0.2) {\n      level = 'low';\n    } else if (currentRatio > 0.6) {\n      level = 'high';\n    } else {\n      level = 'normal';\n    }\n\n    return recommendations[category][level];\n  }\n\n  /**\n   * 预测下周建议\n   */\n  predictNextWeekRecommendations(currentStats: WeeklyStats): string[] {\n    const recommendations: string[] = [];\n\n    // 基于当前趋势预测\n    if (currentStats.trend === 'improving') {\n      recommendations.push('本周平衡状况在改善，继续保持当前的时间安排');\n    } else if (currentStats.trend === 'declining') {\n      recommendations.push('本周平衡状况在下降，建议调整时间分配策略');\n    } else {\n      recommendations.push('本周时间分配稳定，可以尝试优化某些细节');\n    }\n\n    // 基于平均时间给出建议\n    const { averageDaily } = currentStats;\n    const total = averageDaily.work + averageDaily.improvement + averageDaily.entertainment;\n    \n    if (total < 480) { // 少于8小时\n      recommendations.push('每日活跃时间较少，建议增加有意义的活动');\n    } else if (total > 720) { // 超过12小时\n      recommendations.push('每日活跃时间较长，注意适当休息');\n    }\n\n    return recommendations;\n  }\n\n  /**\n   * 获取平衡分数等级描述\n   */\n  getBalanceScoreDescription(score: number): string {\n    if (score >= 90) {\n      return '优秀 - 时间分配非常均衡';\n    } else if (score >= 80) {\n      return '良好 - 时间分配基本均衡';\n    } else if (score >= 70) {\n      return '一般 - 时间分配需要小幅调整';\n    } else if (score >= 60) {\n      return '待改善 - 时间分配需要调整';\n    } else {\n      return '需要改进 - 时间分配严重失衡';\n    }\n  }\n\n  /**\n   * 计算理想的时间分配建议\n   */\n  calculateIdealTimeAllocation(availableHours: number): CategoryRatios {\n    const totalMinutes = availableHours * 60;\n    \n    return {\n      work: Math.round(totalMinutes * 0.6), // 60%\n      improvement: Math.round(totalMinutes * 0.25), // 25%\n      entertainment: Math.round(totalMinutes * 0.15) // 15%\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AAAA;;AAaO,MAAM;IAEX;;GAEC,GACD,MAAM,eAAe,MAAc,EAAE,OAAa,IAAI,MAAM,EAA4B;QACtF,YAAY;QACZ,MAAM,cAAc,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ;QAE5D,SAAS;QACT,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,YAAY,SAAS;QAEzE,SAAS;QACT,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAEhD,OAAO;QACP,MAAM,kBAAkB,IAAI,CAAC,uBAAuB,CAAC,gBAAgB;QAErE,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAc,qBAAqB,MAAc,EAAE,IAAU,EAAwB;QACnF,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE;QACnC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;QAE/B,wBAAwB;QACxB,MAAM,YAA4B;YAChC,MAAM;YACN,aAAa;YACb,eAAe,IAAI,OAAO;QAC5B;QAEA,MAAM,eAAe,UAAU,IAAI,GAAG,UAAU,WAAW,GAAG,UAAU,aAAa;QAErF,MAAM,eAA+B;YACnC,MAAM,UAAU,IAAI,GAAG;YACvB,aAAa,UAAU,WAAW,GAAG;YACrC,eAAe,UAAU,aAAa,GAAG;QAC3C;QAEA,eAAe;QACf,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC;QAElC,OAAO;YACL,WAAW;YACX;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,wBAAwB,SAAyB,EAAkB;QACzE,MAAM,QAAQ,UAAU,IAAI,GAAG,UAAU,WAAW,GAAG,UAAU,aAAa;QAE9E,IAAI,UAAU,GAAG;YACf,OAAO;gBAAE,MAAM;gBAAG,aAAa;gBAAG,eAAe;YAAE;QACrD;QAEA,OAAO;YACL,MAAM,KAAK,KAAK,CAAC,AAAC,UAAU,IAAI,GAAG,QAAS,OAAO;YACnD,aAAa,KAAK,KAAK,CAAC,AAAC,UAAU,WAAW,GAAG,QAAS,OAAO;YACjE,eAAe,KAAK,KAAK,CAAC,AAAC,UAAU,aAAa,GAAG,QAAS,OAAO;QACvE;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAsB,EAAU;QAC5D,yBAAyB;QACzB,MAAM,cAAc;YAAE,MAAM;YAAK,aAAa;YAAM,eAAe;QAAK;QAExE,aAAa;QACb,MAAM,gBAAgB,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,YAAY,IAAI;QAC7D,MAAM,uBAAuB,KAAK,GAAG,CAAC,OAAO,WAAW,GAAG,YAAY,WAAW;QAClF,MAAM,yBAAyB,KAAK,GAAG,CAAC,OAAO,aAAa,GAAG,YAAY,aAAa;QAExF,MAAM;QACN,MAAM,iBAAiB,gBAAgB,uBAAuB;QAE9D,oBAAoB;QACpB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAO,iBAAiB;QAElD,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA;;GAEC,GACD,AAAQ,eAAe,YAA4B,EAAwC;QACzF,iBAAiB;QACjB,WAAW;QACX,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,MAAsB,EAAE,WAAwB,EAAY;QAC1F,MAAM,kBAA4B,EAAE;QAEpC,SAAS;QACT,IAAI,OAAO,IAAI,GAAG,KAAK;YACrB,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,WAAW,GAAG,MAAM;YAC7B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,WAAW,GAAG,MAAM;YACpC,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,aAAa,GAAG,KAAK;YAC9B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,aAAa,GAAG,MAAM;YACtC,gBAAgB,IAAI,CAAC;QACvB;QAEA,QAAQ;QACR,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAChD,IAAI,gBAAgB,IAAI;YACtB,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,gBAAgB,IAAI;YAC7B,gBAAgB,IAAI,CAAC;QACvB,OAAO;YACL,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,iBACJ,MAAc,EACd,QAAsB,EACtB,QAAgB,EACD;QACf,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QAE3B,kBAAkB;QAClB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE,CAAC;IAE7E,UAAU;IACV,8CAA8C;IAC9C,qBAAqB;IACrB,mBAAmB;IACnB,mCAAmC;IACnC,MAAM;IACR;IAEA;;GAEC,GACD,8BAA8B,QAAsB,EAAE,YAAoB,EAAU;QAClF,MAAM,kBAAkB;YACtB,CAAC,mIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,EAAE;gBACtB,KAAK;gBACL,QAAQ;gBACR,MAAM;YACR;YACA,CAAC,mIAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,EAAE;gBAC7B,KAAK;gBACL,QAAQ;gBACR,MAAM;YACR;YACA,CAAC,mIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,EAAE;gBAC/B,KAAK;gBACL,QAAQ;gBACR,MAAM;YACR;QACF;QAEA,IAAI;QACJ,IAAI,eAAe,KAAK;YACtB,QAAQ;QACV,OAAO,IAAI,eAAe,KAAK;YAC7B,QAAQ;QACV,OAAO;YACL,QAAQ;QACV;QAEA,OAAO,eAAe,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA;;GAEC,GACD,+BAA+B,YAAyB,EAAY;QAClE,MAAM,kBAA4B,EAAE;QAEpC,WAAW;QACX,IAAI,aAAa,KAAK,KAAK,aAAa;YACtC,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,aAAa,KAAK,KAAK,aAAa;YAC7C,gBAAgB,IAAI,CAAC;QACvB,OAAO;YACL,gBAAgB,IAAI,CAAC;QACvB;QAEA,aAAa;QACb,MAAM,EAAE,YAAY,EAAE,GAAG;QACzB,MAAM,QAAQ,aAAa,IAAI,GAAG,aAAa,WAAW,GAAG,aAAa,aAAa;QAEvF,IAAI,QAAQ,KAAK;YACf,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,QAAQ,KAAK;YACtB,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAU;QAChD,IAAI,SAAS,IAAI;YACf,OAAO;QACT,OAAO,IAAI,SAAS,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,SAAS,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,SAAS,IAAI;YACtB,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA;;GAEC,GACD,6BAA6B,cAAsB,EAAkB;QACnE,MAAM,eAAe,iBAAiB;QAEtC,OAAO;YACL,MAAM,KAAK,KAAK,CAAC,eAAe;YAChC,aAAa,KAAK,KAAK,CAAC,eAAe;YACvC,eAAe,KAAK,KAAK,CAAC,eAAe,MAAM,MAAM;QACvD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts"], "sourcesContent": ["/**\n * 修复算法\n * 检测和处理推迟的任务，提供修复建议\n */\n\nimport {\n  Task,\n  PostponedTaskAlert,\n  TaskStatus,\n  Priority,\n  classifyQuadrant,\n  isTaskOverdue,\n  getDaysDifference,\n  getRelativeTime,\n  QUADRANTS\n} from '@/shared';\n\nexport class FixAlgorithm {\n\n  /**\n   * 分析推迟的任务\n   */\n  analyzePostponedTasks(tasks: Task[]): PostponedTaskAlert[] {\n    const postponedTasks = tasks.filter(task => \n      task.postponeCount > 0 || \n      task.status === 'postponed' ||\n      isTaskOverdue(task)\n    );\n\n    return postponedTasks.map(task => this.createPostponedAlert(task));\n  }\n\n  /**\n   * 创建推迟任务提醒\n   */\n  private createPostponedAlert(task: Task): PostponedTaskAlert {\n    const daysSinceCreated = getDaysDifference(task.createdAt, new Date());\n    const severity = this.calculateSeverity(task);\n    const recommendation = this.generateRecommendation(task);\n\n    return {\n      taskId: task.id,\n      task,\n      postponeCount: task.postponeCount,\n      lastPostponeDate: task.updatedAt,\n      severity,\n      recommendation\n    };\n  }\n\n  /**\n   * 计算严重程度\n   */\n  private calculateSeverity(task: Task): 'low' | 'medium' | 'high' {\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n    const isOverdue = isTaskOverdue(task);\n    const postponeCount = task.postponeCount;\n\n    // 已过期的任务\n    if (isOverdue) {\n      if (quadrant === QUADRANTS.URGENT_IMPORTANT) {\n        return 'high';\n      } else if (quadrant === QUADRANTS.IMPORTANT_NOT_URGENT || \n                 quadrant === QUADRANTS.URGENT_NOT_IMPORTANT) {\n        return 'medium';\n      } else {\n        return 'low';\n      }\n    }\n\n    // 根据推迟次数判断\n    if (postponeCount >= 3) {\n      return quadrant <= 2 ? 'high' : 'medium';\n    } else if (postponeCount >= 2) {\n      return quadrant === 1 ? 'high' : 'medium';\n    } else {\n      return quadrant === 1 ? 'medium' : 'low';\n    }\n  }\n\n  /**\n   * 生成修复建议\n   */\n  private generateRecommendation(task: Task): string {\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n    const isOverdue = isTaskOverdue(task);\n    const postponeCount = task.postponeCount;\n\n    if (isOverdue) {\n      return this.getOverdueRecommendation(task, quadrant);\n    }\n\n    if (postponeCount >= 3) {\n      return this.getHighPostponeRecommendation(task, quadrant);\n    }\n\n    if (postponeCount >= 1) {\n      return this.getPostponeRecommendation(task, quadrant);\n    }\n\n    return '建议尽快处理此任务';\n  }\n\n  /**\n   * 获取过期任务建议\n   */\n  private getOverdueRecommendation(task: Task, quadrant: number): string {\n    const daysPastDeadline = Math.abs(getDaysDifference(task.deadline, new Date()));\n    \n    switch (quadrant) {\n      case QUADRANTS.URGENT_IMPORTANT:\n        return `🚨 任务已过期${daysPastDeadline}天，需要立即处理！考虑重新评估截止时间或寻求帮助。`;\n      \n      case QUADRANTS.IMPORTANT_NOT_URGENT:\n        return `⚠️ 重要任务已过期${daysPastDeadline}天，建议重新安排优先级，尽快完成。`;\n      \n      case QUADRANTS.URGENT_NOT_IMPORTANT:\n        return `⏰ 紧急任务已过期${daysPastDeadline}天，考虑委托他人处理或重新评估必要性。`;\n      \n      default:\n        return `📅 任务已过期${daysPastDeadline}天，建议评估是否仍需完成，或者删除此任务。`;\n    }\n  }\n\n  /**\n   * 获取高频推迟任务建议\n   */\n  private getHighPostponeRecommendation(task: Task, quadrant: number): string {\n    switch (quadrant) {\n      case QUADRANTS.URGENT_IMPORTANT:\n        return `🔥 此任务已推迟${task.postponeCount}次，建议分解为更小的子任务，或寻求帮助完成。`;\n      \n      case QUADRANTS.IMPORTANT_NOT_URGENT:\n        return `📋 重要任务推迟${task.postponeCount}次，建议设定固定时间块专门处理，避免再次推迟。`;\n      \n      case QUADRANTS.URGENT_NOT_IMPORTANT:\n        return `🤝 紧急任务推迟${task.postponeCount}次，强烈建议委托他人处理或使用自动化工具。`;\n      \n      default:\n        return `🤔 任务推迟${task.postponeCount}次，建议重新评估其必要性，考虑删除或降低优先级。`;\n    }\n  }\n\n  /**\n   * 获取一般推迟任务建议\n   */\n  private getPostponeRecommendation(task: Task, quadrant: number): string {\n    switch (quadrant) {\n      case QUADRANTS.URGENT_IMPORTANT:\n        return '🎯 高优先级任务，建议立即安排时间处理，避免进一步推迟。';\n      \n      case QUADRANTS.IMPORTANT_NOT_URGENT:\n        return '📅 重要任务，建议在日程中安排固定时间，确保按时完成。';\n      \n      case QUADRANTS.URGENT_NOT_IMPORTANT:\n        return '⚡ 考虑委托他人处理，或寻找更高效的解决方案。';\n      \n      default:\n        return '💭 评估任务的实际价值，考虑是否需要继续保留。';\n    }\n  }\n\n  /**\n   * 获取推迟任务的统计信息\n   */\n  getPostponedTasksStats(tasks: Task[]): {\n    totalPostponed: number;\n    byCategory: Record<string, number>;\n    bySeverity: Record<string, number>;\n    averagePostponeCount: number;\n  } {\n    const postponedTasks = tasks.filter(task => \n      task.postponeCount > 0 || task.status === 'postponed'\n    );\n\n    const byCategory = postponedTasks.reduce((acc, task) => {\n      acc[task.category] = (acc[task.category] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const alerts = this.analyzePostponedTasks(postponedTasks);\n    const bySeverity = alerts.reduce((acc, alert) => {\n      acc[alert.severity] = (acc[alert.severity] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    const totalPostponeCount = postponedTasks.reduce((sum, task) => sum + task.postponeCount, 0);\n    const averagePostponeCount = postponedTasks.length > 0 ? \n      Math.round((totalPostponeCount / postponedTasks.length) * 10) / 10 : 0;\n\n    return {\n      totalPostponed: postponedTasks.length,\n      byCategory,\n      bySeverity,\n      averagePostponeCount\n    };\n  }\n\n  /**\n   * 建议任务重新安排策略\n   */\n  suggestRescheduleStrategy(task: Task): {\n    strategy: string;\n    newDeadline?: Date;\n    breakDown?: string[];\n    delegation?: string;\n  } {\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n    const postponeCount = task.postponeCount;\n\n    // 高频推迟的任务需要特殊处理\n    if (postponeCount >= 3) {\n      return this.getHighPostponeStrategy(task, quadrant);\n    }\n\n    // 一般推迟任务的策略\n    return this.getGeneralStrategy(task, quadrant);\n  }\n\n  /**\n   * 获取高频推迟任务的策略\n   */\n  private getHighPostponeStrategy(task: Task, quadrant: number): any {\n    switch (quadrant) {\n      case QUADRANTS.URGENT_IMPORTANT:\n        return {\n          strategy: 'immediate_action',\n          breakDown: [\n            '将任务分解为15-30分钟的小块',\n            '立即开始第一个小任务',\n            '寻求同事或朋友的帮助',\n            '移除所有干扰因素'\n          ]\n        };\n\n      case QUADRANTS.IMPORTANT_NOT_URGENT:\n        const newDeadline = new Date();\n        newDeadline.setDate(newDeadline.getDate() + 7);\n        return {\n          strategy: 'scheduled_focus',\n          newDeadline,\n          breakDown: [\n            '设定每日固定时间处理',\n            '使用番茄工作法',\n            '设置进度检查点',\n            '奖励机制激励完成'\n          ]\n        };\n\n      case QUADRANTS.URGENT_NOT_IMPORTANT:\n        return {\n          strategy: 'delegate_or_automate',\n          delegation: '寻找可以委托的人员或自动化工具',\n          breakDown: [\n            '评估委托的可能性',\n            '寻找自动化解决方案',\n            '如无法委托，快速批量处理'\n          ]\n        };\n\n      default:\n        return {\n          strategy: 'eliminate_or_defer',\n          breakDown: [\n            '重新评估任务的必要性',\n            '考虑完全删除此任务',\n            '如必须保留，设定更宽松的时间线'\n          ]\n        };\n    }\n  }\n\n  /**\n   * 获取一般任务的重新安排策略\n   */\n  private getGeneralStrategy(task: Task, quadrant: number): any {\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    switch (quadrant) {\n      case QUADRANTS.URGENT_IMPORTANT:\n        return {\n          strategy: 'priority_scheduling',\n          newDeadline: tomorrow,\n          breakDown: ['安排在最佳工作时间', '清除其他干扰', '专注完成']\n        };\n\n      case QUADRANTS.IMPORTANT_NOT_URGENT:\n        const nextWeek = new Date();\n        nextWeek.setDate(nextWeek.getDate() + 7);\n        return {\n          strategy: 'planned_execution',\n          newDeadline: nextWeek,\n          breakDown: ['制定详细计划', '分阶段执行', '定期检查进度']\n        };\n\n      default:\n        return {\n          strategy: 'flexible_scheduling',\n          breakDown: ['安排在空闲时间', '与其他类似任务批量处理']\n        };\n    }\n  }\n\n  /**\n   * 检查任务是否需要紧急关注\n   */\n  needsUrgentAttention(task: Task): boolean {\n    const isOverdue = isTaskOverdue(task);\n    const quadrant = classifyQuadrant(task.importance, task.urgency);\n    const highPostponeCount = task.postponeCount >= 3;\n\n    return isOverdue || \n           (quadrant === QUADRANTS.URGENT_IMPORTANT && task.postponeCount >= 1) ||\n           (highPostponeCount && quadrant <= 2);\n  }\n\n  /**\n   * 生成修复行动计划\n   */\n  generateActionPlan(alerts: PostponedTaskAlert[]): string[] {\n    const actionPlan: string[] = [];\n    \n    const highSeverityTasks = alerts.filter(alert => alert.severity === 'high');\n    const mediumSeverityTasks = alerts.filter(alert => alert.severity === 'medium');\n    \n    if (highSeverityTasks.length > 0) {\n      actionPlan.push(`🚨 立即处理 ${highSeverityTasks.length} 个高优先级推迟任务`);\n    }\n    \n    if (mediumSeverityTasks.length > 0) {\n      actionPlan.push(`⚠️ 本周内处理 ${mediumSeverityTasks.length} 个中等优先级推迟任务`);\n    }\n    \n    if (alerts.length > 5) {\n      actionPlan.push('📋 考虑重新评估任务管理策略，避免过多任务推迟');\n    }\n    \n    return actionPlan;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AAAA;AAAA;;AAYO,MAAM;IAEX;;GAEC,GACD,sBAAsB,KAAa,EAAwB;QACzD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAClC,KAAK,aAAa,GAAG,KACrB,KAAK,MAAM,KAAK,eAChB,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;QAGhB,OAAO,eAAe,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,oBAAoB,CAAC;IAC9D;IAEA;;GAEC,GACD,AAAQ,qBAAqB,IAAU,EAAsB;QAC3D,MAAM,mBAAmB,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,SAAS,EAAE,IAAI;QAC/D,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC;QACxC,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC;QAEnD,OAAO;YACL,QAAQ,KAAK,EAAE;YACf;YACA,eAAe,KAAK,aAAa;YACjC,kBAAkB,KAAK,SAAS;YAChC;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,kBAAkB,IAAU,EAA6B;QAC/D,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAC/D,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,MAAM,gBAAgB,KAAK,aAAa;QAExC,SAAS;QACT,IAAI,WAAW;YACb,IAAI,aAAa,mIAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;gBAC3C,OAAO;YACT,OAAO,IAAI,aAAa,mIAAA,CAAA,YAAS,CAAC,oBAAoB,IAC3C,aAAa,mIAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE;gBACtD,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,WAAW;QACX,IAAI,iBAAiB,GAAG;YACtB,OAAO,YAAY,IAAI,SAAS;QAClC,OAAO,IAAI,iBAAiB,GAAG;YAC7B,OAAO,aAAa,IAAI,SAAS;QACnC,OAAO;YACL,OAAO,aAAa,IAAI,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,IAAU,EAAU;QACjD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAC/D,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,MAAM,gBAAgB,KAAK,aAAa;QAExC,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM;QAC7C;QAEA,IAAI,iBAAiB,GAAG;YACtB,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM;QAClD;QAEA,IAAI,iBAAiB,GAAG;YACtB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;QAC9C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,yBAAyB,IAAU,EAAE,QAAgB,EAAU;QACrE,MAAM,mBAAmB,KAAK,GAAG,CAAC,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,QAAQ,EAAE,IAAI;QAEvE,OAAQ;YACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;gBAC7B,OAAO,CAAC,QAAQ,EAAE,iBAAiB,yBAAyB,CAAC;YAE/D,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO,CAAC,UAAU,EAAE,iBAAiB,iBAAiB,CAAC;YAEzD,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO,CAAC,SAAS,EAAE,iBAAiB,mBAAmB,CAAC;YAE1D;gBACE,OAAO,CAAC,QAAQ,EAAE,iBAAiB,qBAAqB,CAAC;QAC7D;IACF;IAEA;;GAEC,GACD,AAAQ,8BAA8B,IAAU,EAAE,QAAgB,EAAU;QAC1E,OAAQ;YACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;gBAC7B,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,CAAC,sBAAsB,CAAC;YAE/D,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,CAAC,uBAAuB,CAAC;YAEhE,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,CAAC,qBAAqB,CAAC;YAE9D;gBACE,OAAO,CAAC,OAAO,EAAE,KAAK,aAAa,CAAC,wBAAwB,CAAC;QACjE;IACF;IAEA;;GAEC,GACD,AAAQ,0BAA0B,IAAU,EAAE,QAAgB,EAAU;QACtE,OAAQ;YACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;gBAC7B,OAAO;YAET,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO;YAET,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO;YAET;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,uBAAuB,KAAa,EAKlC;QACA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAClC,KAAK,aAAa,GAAG,KAAK,KAAK,MAAM,KAAK;QAG5C,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK;YAC7C,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC;QAC1C,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;YACrC,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI;YACnD,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,qBAAqB,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE;QAC1F,MAAM,uBAAuB,eAAe,MAAM,GAAG,IACnD,KAAK,KAAK,CAAC,AAAC,qBAAqB,eAAe,MAAM,GAAI,MAAM,KAAK;QAEvE,OAAO;YACL,gBAAgB,eAAe,MAAM;YACrC;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,0BAA0B,IAAU,EAKlC;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAC/D,MAAM,gBAAgB,KAAK,aAAa;QAExC,gBAAgB;QAChB,IAAI,iBAAiB,GAAG;YACtB,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM;QAC5C;QAEA,YAAY;QACZ,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM;IACvC;IAEA;;GAEC,GACD,AAAQ,wBAAwB,IAAU,EAAE,QAAgB,EAAO;QACjE,OAAQ;YACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;gBAC7B,OAAO;oBACL,UAAU;oBACV,WAAW;wBACT;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,MAAM,cAAc,IAAI;gBACxB,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;gBAC5C,OAAO;oBACL,UAAU;oBACV;oBACA,WAAW;wBACT;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,WAAW;wBACT;wBACA;wBACA;qBACD;gBACH;YAEF;gBACE,OAAO;oBACL,UAAU;oBACV,WAAW;wBACT;wBACA;wBACA;qBACD;gBACH;QACJ;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAE,QAAgB,EAAO;QAC5D,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,OAAQ;YACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;gBAC7B,OAAO;oBACL,UAAU;oBACV,aAAa;oBACb,WAAW;wBAAC;wBAAa;wBAAU;qBAAO;gBAC5C;YAEF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;gBACjC,MAAM,WAAW,IAAI;gBACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBACtC,OAAO;oBACL,UAAU;oBACV,aAAa;oBACb,WAAW;wBAAC;wBAAU;wBAAS;qBAAS;gBAC1C;YAEF;gBACE,OAAO;oBACL,UAAU;oBACV,WAAW;wBAAC;wBAAW;qBAAc;gBACvC;QACJ;IACF;IAEA;;GAEC,GACD,qBAAqB,IAAU,EAAW;QACxC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;QAC/D,MAAM,oBAAoB,KAAK,aAAa,IAAI;QAEhD,OAAO,aACC,aAAa,mIAAA,CAAA,YAAS,CAAC,gBAAgB,IAAI,KAAK,aAAa,IAAI,KACjE,qBAAqB,YAAY;IAC3C;IAEA;;GAEC,GACD,mBAAmB,MAA4B,EAAY;QACzD,MAAM,aAAuB,EAAE;QAE/B,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QACpE,MAAM,sBAAsB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QAEtE,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,WAAW,IAAI,CAAC,CAAC,QAAQ,EAAE,kBAAkB,MAAM,CAAC,UAAU,CAAC;QACjE;QAEA,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,WAAW,IAAI,CAAC,CAAC,SAAS,EAAE,oBAAoB,MAAM,CAAC,WAAW,CAAC;QACrE;QAEA,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts"], "sourcesContent": ["/**\n * 时间调整算法\n * 当任务超时时，动态调整后续任务的时间安排\n */\n\nimport {\n  Task,\n  TimeSlot,\n  AdjustmentResult,\n  UserTimeConfig,\n  Priority,\n  classifyQuadrant,\n  isTimeRangeOverlap,\n  getMinutesBetween,\n  QUADRANTS\n} from '@/shared';\n\nexport class TimeAdjustmentAlgorithm {\n\n  /**\n   * 当任务超时时调整后续安排\n   */\n  adjustForOverrun(\n    overrunTask: Task,\n    actualDuration: number,\n    currentSchedule: TimeSlot[],\n    userTimeConfig?: UserTimeConfig\n  ): AdjustmentResult {\n    const originalDuration = overrunTask.estimatedDuration;\n    const overrunMinutes = actualDuration - originalDuration;\n\n    if (overrunMinutes <= 0) {\n      return {\n        success: true,\n        adjustedSchedule: currentSchedule,\n        affectedTasks: [],\n        message: '任务按时完成，无需调整',\n        impactScore: 0\n      };\n    }\n\n    // 找到超时任务在当前安排中的位置\n    const overrunSlotIndex = currentSchedule.findIndex(slot => slot.task.id === overrunTask.id);\n    \n    if (overrunSlotIndex === -1) {\n      return {\n        success: false,\n        adjustedSchedule: currentSchedule,\n        affectedTasks: [],\n        message: '未找到超时任务的时间安排',\n        impactScore: 0\n      };\n    }\n\n    // 计算调整策略\n    const adjustmentStrategy = this.calculateAdjustmentStrategy(\n      overrunMinutes,\n      currentSchedule,\n      overrunSlotIndex,\n      userTimeConfig\n    );\n\n    return this.applyAdjustmentStrategy(\n      adjustmentStrategy,\n      currentSchedule,\n      overrunSlotIndex,\n      overrunMinutes\n    );\n  }\n\n  /**\n   * 计算调整策略\n   */\n  private calculateAdjustmentStrategy(\n    overrunMinutes: number,\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    userTimeConfig?: UserTimeConfig\n  ): 'compress' | 'postpone' | 'reschedule' | 'hybrid' {\n    const remainingSlots = schedule.slice(overrunIndex + 1);\n    \n    if (remainingSlots.length === 0) {\n      return 'postpone';\n    }\n\n    // 计算可压缩的时间\n    const compressibleTime = this.calculateCompressibleTime(remainingSlots);\n    \n    if (compressibleTime >= overrunMinutes) {\n      return 'compress';\n    } else if (compressibleTime >= overrunMinutes * 0.6) {\n      return 'hybrid'; // 部分压缩，部分推迟\n    } else {\n      return 'reschedule';\n    }\n  }\n\n  /**\n   * 计算可压缩的时间\n   */\n  private calculateCompressibleTime(slots: TimeSlot[]): number {\n    let compressibleTime = 0;\n\n    for (const slot of slots) {\n      const task = slot.task;\n      const quadrant = classifyQuadrant(task.importance, task.urgency);\n      const duration = getMinutesBetween(slot.startTime, slot.endTime);\n\n      // 根据任务重要性确定可压缩比例\n      let compressRatio = 0;\n      switch (quadrant) {\n        case QUADRANTS.URGENT_IMPORTANT:\n          compressRatio = 0.1; // 最多压缩10%\n          break;\n        case QUADRANTS.IMPORTANT_NOT_URGENT:\n          compressRatio = 0.2; // 最多压缩20%\n          break;\n        case QUADRANTS.URGENT_NOT_IMPORTANT:\n          compressRatio = 0.3; // 最多压缩30%\n          break;\n        case QUADRANTS.NOT_URGENT_NOT_IMPORTANT:\n          compressRatio = 0.5; // 最多压缩50%\n          break;\n      }\n\n      compressibleTime += duration * compressRatio;\n    }\n\n    return Math.floor(compressibleTime);\n  }\n\n  /**\n   * 应用调整策略\n   */\n  private applyAdjustmentStrategy(\n    strategy: string,\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    overrunMinutes: number\n  ): AdjustmentResult {\n    const adjustedSchedule = [...schedule];\n    const affectedTasks: Task[] = [];\n    let impactScore = 0;\n\n    switch (strategy) {\n      case 'compress':\n        return this.applyCompressionStrategy(adjustedSchedule, overrunIndex, overrunMinutes);\n      \n      case 'postpone':\n        return this.applyPostponeStrategy(adjustedSchedule, overrunIndex, overrunMinutes);\n      \n      case 'reschedule':\n        return this.applyRescheduleStrategy(adjustedSchedule, overrunIndex, overrunMinutes);\n      \n      case 'hybrid':\n        return this.applyHybridStrategy(adjustedSchedule, overrunIndex, overrunMinutes);\n      \n      default:\n        return {\n          success: false,\n          adjustedSchedule: schedule,\n          affectedTasks: [],\n          message: '未知的调整策略',\n          impactScore: 0\n        };\n    }\n  }\n\n  /**\n   * 应用压缩策略\n   */\n  private applyCompressionStrategy(\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    overrunMinutes: number\n  ): AdjustmentResult {\n    const affectedTasks: Task[] = [];\n    let remainingOverrun = overrunMinutes;\n    let impactScore = 0;\n\n    // 延长超时任务的结束时间\n    schedule[overrunIndex].endTime = new Date(\n      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000\n    );\n\n    // 压缩后续任务\n    for (let i = overrunIndex + 1; i < schedule.length && remainingOverrun > 0; i++) {\n      const slot = schedule[i];\n      const task = slot.task;\n      const quadrant = classifyQuadrant(task.importance, task.urgency);\n      const duration = getMinutesBetween(slot.startTime, slot.endTime);\n\n      // 计算压缩比例\n      let compressRatio = 0;\n      switch (quadrant) {\n        case QUADRANTS.URGENT_IMPORTANT:\n          compressRatio = 0.1;\n          impactScore += 10;\n          break;\n        case QUADRANTS.IMPORTANT_NOT_URGENT:\n          compressRatio = 0.2;\n          impactScore += 6;\n          break;\n        case QUADRANTS.URGENT_NOT_IMPORTANT:\n          compressRatio = 0.3;\n          impactScore += 4;\n          break;\n        case QUADRANTS.NOT_URGENT_NOT_IMPORTANT:\n          compressRatio = 0.5;\n          impactScore += 2;\n          break;\n      }\n\n      const maxCompress = Math.floor(duration * compressRatio);\n      const actualCompress = Math.min(maxCompress, remainingOverrun);\n\n      if (actualCompress > 0) {\n        // 调整开始时间（向后推迟）\n        slot.startTime = new Date(slot.startTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000);\n        \n        // 调整结束时间（压缩持续时间）\n        slot.endTime = new Date(slot.endTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000 - actualCompress * 60 * 1000);\n        \n        affectedTasks.push(task);\n        remainingOverrun -= actualCompress;\n      } else {\n        // 只是向后推迟，不压缩\n        slot.startTime = new Date(slot.startTime.getTime() + remainingOverrun * 60 * 1000);\n        slot.endTime = new Date(slot.endTime.getTime() + remainingOverrun * 60 * 1000);\n      }\n    }\n\n    return {\n      success: remainingOverrun === 0,\n      adjustedSchedule: schedule,\n      affectedTasks,\n      message: remainingOverrun === 0 \n        ? `成功通过压缩后续任务调整了${overrunMinutes}分钟的超时`\n        : `部分调整成功，仍有${remainingOverrun}分钟需要其他处理`,\n      impactScore\n    };\n  }\n\n  /**\n   * 应用推迟策略\n   */\n  private applyPostponeStrategy(\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    overrunMinutes: number\n  ): AdjustmentResult {\n    // 延长超时任务的结束时间\n    schedule[overrunIndex].endTime = new Date(\n      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000\n    );\n\n    // 将后续所有任务向后推迟\n    for (let i = overrunIndex + 1; i < schedule.length; i++) {\n      schedule[i].startTime = new Date(schedule[i].startTime.getTime() + overrunMinutes * 60 * 1000);\n      schedule[i].endTime = new Date(schedule[i].endTime.getTime() + overrunMinutes * 60 * 1000);\n    }\n\n    const affectedTasks = schedule.slice(overrunIndex + 1).map(slot => slot.task);\n\n    return {\n      success: true,\n      adjustedSchedule: schedule,\n      affectedTasks,\n      message: `所有后续任务向后推迟${overrunMinutes}分钟`,\n      impactScore: affectedTasks.length * 3 // 推迟的影响相对较小\n    };\n  }\n\n  /**\n   * 应用重新安排策略\n   */\n  private applyRescheduleStrategy(\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    overrunMinutes: number\n  ): AdjustmentResult {\n    const affectedTasks: Task[] = [];\n    const remainingSlots = schedule.slice(overrunIndex + 1);\n    \n    // 延长超时任务\n    schedule[overrunIndex].endTime = new Date(\n      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000\n    );\n\n    // 选择低优先级任务移到明天\n    const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);\n    \n    // 移除被重新安排的任务\n    const newSchedule = schedule.filter(slot => \n      !tasksToReschedule.some(task => task.id === slot.task.id)\n    );\n\n    // 调整剩余任务的时间\n    let timeOffset = overrunMinutes;\n    for (let i = overrunIndex + 1; i < newSchedule.length; i++) {\n      newSchedule[i].startTime = new Date(newSchedule[i].startTime.getTime() + timeOffset * 60 * 1000);\n      newSchedule[i].endTime = new Date(newSchedule[i].endTime.getTime() + timeOffset * 60 * 1000);\n    }\n\n    return {\n      success: true,\n      adjustedSchedule: newSchedule,\n      affectedTasks: tasksToReschedule,\n      message: `${tasksToReschedule.length}个低优先级任务被重新安排到明天`,\n      impactScore: tasksToReschedule.length * 8 // 重新安排的影响较大\n    };\n  }\n\n  /**\n   * 应用混合策略\n   */\n  private applyHybridStrategy(\n    schedule: TimeSlot[],\n    overrunIndex: number,\n    overrunMinutes: number\n  ): AdjustmentResult {\n    // 先尝试压缩\n    const compressResult = this.applyCompressionStrategy([...schedule], overrunIndex, overrunMinutes);\n    \n    if (compressResult.success) {\n      return compressResult;\n    }\n\n    // 如果压缩不够，再结合重新安排\n    const remainingOverrun = overrunMinutes - this.calculateCompressibleTime(schedule.slice(overrunIndex + 1));\n    const rescheduleResult = this.applyRescheduleStrategy([...schedule], overrunIndex, remainingOverrun);\n\n    return {\n      success: true,\n      adjustedSchedule: rescheduleResult.adjustedSchedule,\n      affectedTasks: [...compressResult.affectedTasks, ...rescheduleResult.affectedTasks],\n      message: `采用混合策略：压缩部分任务，重新安排${rescheduleResult.affectedTasks.length}个任务`,\n      impactScore: compressResult.impactScore + rescheduleResult.impactScore\n    };\n  }\n\n  /**\n   * 选择需要重新安排的任务\n   */\n  private selectTasksToReschedule(slots: TimeSlot[], targetMinutes: number): Task[] {\n    // 按优先级排序，选择低优先级任务\n    const sortedSlots = [...slots].sort((a, b) => {\n      const aQuadrant = classifyQuadrant(a.task.importance, a.task.urgency);\n      const bQuadrant = classifyQuadrant(b.task.importance, b.task.urgency);\n      return bQuadrant - aQuadrant; // 降序，低优先级在前\n    });\n\n    const tasksToReschedule: Task[] = [];\n    let freedTime = 0;\n\n    for (const slot of sortedSlots) {\n      if (freedTime >= targetMinutes) break;\n      \n      const duration = getMinutesBetween(slot.startTime, slot.endTime);\n      tasksToReschedule.push(slot.task);\n      freedTime += duration;\n    }\n\n    return tasksToReschedule;\n  }\n\n  /**\n   * 预测调整的影响\n   */\n  predictAdjustmentImpact(\n    overrunMinutes: number,\n    schedule: TimeSlot[],\n    overrunIndex: number\n  ): {\n    strategy: string;\n    affectedTasksCount: number;\n    impactScore: number;\n    description: string;\n  } {\n    const remainingSlots = schedule.slice(overrunIndex + 1);\n    const compressibleTime = this.calculateCompressibleTime(remainingSlots);\n\n    if (compressibleTime >= overrunMinutes) {\n      return {\n        strategy: 'compress',\n        affectedTasksCount: remainingSlots.length,\n        impactScore: remainingSlots.length * 3,\n        description: '通过压缩后续任务时间来调整'\n      };\n    } else if (remainingSlots.length === 0) {\n      return {\n        strategy: 'postpone',\n        affectedTasksCount: 0,\n        impactScore: 0,\n        description: '无后续任务，无需调整'\n      };\n    } else {\n      const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);\n      return {\n        strategy: 'reschedule',\n        affectedTasksCount: tasksToReschedule.length,\n        impactScore: tasksToReschedule.length * 8,\n        description: `${tasksToReschedule.length}个任务将被重新安排`\n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AAAA;AAAA;;AAYO,MAAM;IAEX;;GAEC,GACD,iBACE,WAAiB,EACjB,cAAsB,EACtB,eAA2B,EAC3B,cAA+B,EACb;QAClB,MAAM,mBAAmB,YAAY,iBAAiB;QACtD,MAAM,iBAAiB,iBAAiB;QAExC,IAAI,kBAAkB,GAAG;YACvB,OAAO;gBACL,SAAS;gBACT,kBAAkB;gBAClB,eAAe,EAAE;gBACjB,SAAS;gBACT,aAAa;YACf;QACF;QAEA,kBAAkB;QAClB,MAAM,mBAAmB,gBAAgB,SAAS,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,YAAY,EAAE;QAE1F,IAAI,qBAAqB,CAAC,GAAG;YAC3B,OAAO;gBACL,SAAS;gBACT,kBAAkB;gBAClB,eAAe,EAAE;gBACjB,SAAS;gBACT,aAAa;YACf;QACF;QAEA,SAAS;QACT,MAAM,qBAAqB,IAAI,CAAC,2BAA2B,CACzD,gBACA,iBACA,kBACA;QAGF,OAAO,IAAI,CAAC,uBAAuB,CACjC,oBACA,iBACA,kBACA;IAEJ;IAEA;;GAEC,GACD,AAAQ,4BACN,cAAsB,EACtB,QAAoB,EACpB,YAAoB,EACpB,cAA+B,EACoB;QACnD,MAAM,iBAAiB,SAAS,KAAK,CAAC,eAAe;QAErD,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO;QACT;QAEA,WAAW;QACX,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC;QAExD,IAAI,oBAAoB,gBAAgB;YACtC,OAAO;QACT,OAAO,IAAI,oBAAoB,iBAAiB,KAAK;YACnD,OAAO,UAAU,YAAY;QAC/B,OAAO;YACL,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,0BAA0B,KAAiB,EAAU;QAC3D,IAAI,mBAAmB;QAEvB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,OAAO;YAE/D,iBAAiB;YACjB,IAAI,gBAAgB;YACpB,OAAQ;gBACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;oBAC7B,gBAAgB,KAAK,UAAU;oBAC/B;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;oBACjC,gBAAgB,KAAK,UAAU;oBAC/B;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;oBACjC,gBAAgB,KAAK,UAAU;oBAC/B;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,wBAAwB;oBACrC,gBAAgB,KAAK,UAAU;oBAC/B;YACJ;YAEA,oBAAoB,WAAW;QACjC;QAEA,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA;;GAEC,GACD,AAAQ,wBACN,QAAgB,EAChB,QAAoB,EACpB,YAAoB,EACpB,cAAsB,EACJ;QAClB,MAAM,mBAAmB;eAAI;SAAS;QACtC,MAAM,gBAAwB,EAAE;QAChC,IAAI,cAAc;QAElB,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,cAAc;YAEvE,KAAK;gBACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,cAAc;YAEpE,KAAK;gBACH,OAAO,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,cAAc;YAEtE,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,cAAc;YAElE;gBACE,OAAO;oBACL,SAAS;oBACT,kBAAkB;oBAClB,eAAe,EAAE;oBACjB,SAAS;oBACT,aAAa;gBACf;QACJ;IACF;IAEA;;GAEC,GACD,AAAQ,yBACN,QAAoB,EACpB,YAAoB,EACpB,cAAsB,EACJ;QAClB,MAAM,gBAAwB,EAAE;QAChC,IAAI,mBAAmB;QACvB,IAAI,cAAc;QAElB,cAAc;QACd,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,KACnC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,KAAK,iBAAiB,KAAK;QAGnE,SAAS;QACT,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,SAAS,MAAM,IAAI,mBAAmB,GAAG,IAAK;YAC/E,MAAM,OAAO,QAAQ,CAAC,EAAE;YACxB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,OAAO;YAE/D,SAAS;YACT,IAAI,gBAAgB;YACpB,OAAQ;gBACN,KAAK,mIAAA,CAAA,YAAS,CAAC,gBAAgB;oBAC7B,gBAAgB;oBAChB,eAAe;oBACf;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;oBACjC,gBAAgB;oBAChB,eAAe;oBACf;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,oBAAoB;oBACjC,gBAAgB;oBAChB,eAAe;oBACf;gBACF,KAAK,mIAAA,CAAA,YAAS,CAAC,wBAAwB;oBACrC,gBAAgB;oBAChB,eAAe;oBACf;YACJ;YAEA,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW;YAC1C,MAAM,iBAAiB,KAAK,GAAG,CAAC,aAAa;YAE7C,IAAI,iBAAiB,GAAG;gBACtB,eAAe;gBACf,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,CAAC,OAAO,KAAK,CAAC,mBAAmB,cAAc,IAAI,KAAK;gBAEhG,iBAAiB;gBACjB,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,CAAC,OAAO,KAAK,CAAC,mBAAmB,cAAc,IAAI,KAAK,OAAO,iBAAiB,KAAK;gBAEzH,cAAc,IAAI,CAAC;gBACnB,oBAAoB;YACtB,OAAO;gBACL,aAAa;gBACb,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,CAAC,OAAO,KAAK,mBAAmB,KAAK;gBAC7E,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,CAAC,OAAO,KAAK,mBAAmB,KAAK;YAC3E;QACF;QAEA,OAAO;YACL,SAAS,qBAAqB;YAC9B,kBAAkB;YAClB;YACA,SAAS,qBAAqB,IAC1B,CAAC,aAAa,EAAE,eAAe,KAAK,CAAC,GACrC,CAAC,SAAS,EAAE,iBAAiB,QAAQ,CAAC;YAC1C;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBACN,QAAoB,EACpB,YAAoB,EACpB,cAAsB,EACJ;QAClB,cAAc;QACd,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,KACnC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,KAAK,iBAAiB,KAAK;QAGnE,cAAc;QACd,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACvD,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,KAAK,iBAAiB,KAAK;YACzF,QAAQ,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,iBAAiB,KAAK;QACvF;QAEA,MAAM,gBAAgB,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAE5E,OAAO;YACL,SAAS;YACT,kBAAkB;YAClB;YACA,SAAS,CAAC,UAAU,EAAE,eAAe,EAAE,CAAC;YACxC,aAAa,cAAc,MAAM,GAAG,EAAE,YAAY;QACpD;IACF;IAEA;;GAEC,GACD,AAAQ,wBACN,QAAoB,EACpB,YAAoB,EACpB,cAAsB,EACJ;QAClB,MAAM,gBAAwB,EAAE;QAChC,MAAM,iBAAiB,SAAS,KAAK,CAAC,eAAe;QAErD,SAAS;QACT,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,KACnC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,KAAK,iBAAiB,KAAK;QAGnE,eAAe;QACf,MAAM,oBAAoB,IAAI,CAAC,uBAAuB,CAAC,gBAAgB;QAEvE,aAAa;QACb,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,OAClC,CAAC,kBAAkB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE;QAG1D,YAAY;QACZ,IAAI,aAAa;QACjB,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC1D,WAAW,CAAC,EAAE,CAAC,SAAS,GAAG,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,KAAK,aAAa,KAAK;YAC3F,WAAW,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,aAAa,KAAK;QACzF;QAEA,OAAO;YACL,SAAS;YACT,kBAAkB;YAClB,eAAe;YACf,SAAS,GAAG,kBAAkB,MAAM,CAAC,eAAe,CAAC;YACrD,aAAa,kBAAkB,MAAM,GAAG,EAAE,YAAY;QACxD;IACF;IAEA;;GAEC,GACD,AAAQ,oBACN,QAAoB,EACpB,YAAoB,EACpB,cAAsB,EACJ;QAClB,QAAQ;QACR,MAAM,iBAAiB,IAAI,CAAC,wBAAwB,CAAC;eAAI;SAAS,EAAE,cAAc;QAElF,IAAI,eAAe,OAAO,EAAE;YAC1B,OAAO;QACT;QAEA,iBAAiB;QACjB,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,yBAAyB,CAAC,SAAS,KAAK,CAAC,eAAe;QACvG,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC;eAAI;SAAS,EAAE,cAAc;QAEnF,OAAO;YACL,SAAS;YACT,kBAAkB,iBAAiB,gBAAgB;YACnD,eAAe;mBAAI,eAAe,aAAa;mBAAK,iBAAiB,aAAa;aAAC;YACnF,SAAS,CAAC,kBAAkB,EAAE,iBAAiB,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;YACxE,aAAa,eAAe,WAAW,GAAG,iBAAiB,WAAW;QACxE;IACF;IAEA;;GAEC,GACD,AAAQ,wBAAwB,KAAiB,EAAE,aAAqB,EAAU;QAChF,kBAAkB;QAClB,MAAM,cAAc;eAAI;SAAM,CAAC,IAAI,CAAC,CAAC,GAAG;YACtC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,OAAO;YACpE,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,OAAO;YACpE,OAAO,YAAY,WAAW,YAAY;QAC5C;QAEA,MAAM,oBAA4B,EAAE;QACpC,IAAI,YAAY;QAEhB,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,aAAa,eAAe;YAEhC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,OAAO;YAC/D,kBAAkB,IAAI,CAAC,KAAK,IAAI;YAChC,aAAa;QACf;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,wBACE,cAAsB,EACtB,QAAoB,EACpB,YAAoB,EAMpB;QACA,MAAM,iBAAiB,SAAS,KAAK,CAAC,eAAe;QACrD,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC;QAExD,IAAI,oBAAoB,gBAAgB;YACtC,OAAO;gBACL,UAAU;gBACV,oBAAoB,eAAe,MAAM;gBACzC,aAAa,eAAe,MAAM,GAAG;gBACrC,aAAa;YACf;QACF,OAAO,IAAI,eAAe,MAAM,KAAK,GAAG;YACtC,OAAO;gBACL,UAAU;gBACV,oBAAoB;gBACpB,aAAa;gBACb,aAAa;YACf;QACF,OAAO;YACL,MAAM,oBAAoB,IAAI,CAAC,uBAAuB,CAAC,gBAAgB;YACvE,OAAO;gBACL,UAAU;gBACV,oBAAoB,kBAAkB,MAAM;gBAC5C,aAAa,kBAAkB,MAAM,GAAG;gBACxC,aAAa,GAAG,kBAAkB,MAAM,CAAC,SAAS,CAAC;YACrD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts"], "sourcesContent": ["/**\n * 算法协调器\n * 统一管理和协调所有智能规划算法\n */\n\nimport {\n  Task,\n  DailySchedule,\n  BalanceAnalysis,\n  PostponedTaskAlert,\n  AdjustmentResult,\n  UserTimeConfig,\n  TimeSlot\n} from '@/shared';\n\nimport { PlanningAlgorithm } from '../algorithms/PlanningAlgorithm';\nimport { BalanceAlgorithm } from '../algorithms/BalanceAlgorithm';\nimport { FixAlgorithm } from '../algorithms/FixAlgorithm';\nimport { TimeAdjustmentAlgorithm } from '../algorithms/TimeAdjustmentAlgorithm';\n\nexport class AlgorithmCoordinator {\n  private planningAlgorithm: PlanningAlgorithm;\n  private balanceAlgorithm: BalanceAlgorithm;\n  private fixAlgorithm: FixAlgorithm;\n  private timeAdjustmentAlgorithm: TimeAdjustmentAlgorithm;\n\n  constructor() {\n    this.planningAlgorithm = new PlanningAlgorithm();\n    this.balanceAlgorithm = new BalanceAlgorithm();\n    this.fixAlgorithm = new FixAlgorithm();\n    this.timeAdjustmentAlgorithm = new TimeAdjustmentAlgorithm();\n  }\n\n  // ============================================================================\n  // 时间规划相关方法\n  // ============================================================================\n\n  /**\n   * 生成智能的每日时间安排\n   */\n  async generateDailySchedule(\n    tasks: Task[], \n    userTimeConfig?: UserTimeConfig\n  ): Promise<DailySchedule> {\n    try {\n      // 1. 检查推迟任务并给出警告\n      const postponedAlerts = this.fixAlgorithm.analyzePostponedTasks(tasks);\n      const urgentAlerts = postponedAlerts.filter(alert => \n        this.fixAlgorithm.needsUrgentAttention(alert.task)\n      );\n\n      // 2. 生成基础时间安排\n      const schedule = this.planningAlgorithm.generateDailySchedule(tasks, userTimeConfig);\n\n      // 3. 如果有紧急推迟任务，调整安排优先级\n      if (urgentAlerts.length > 0) {\n        console.log(`检测到 ${urgentAlerts.length} 个需要紧急处理的推迟任务`);\n        // 这里可以进一步优化安排逻辑\n      }\n\n      return schedule;\n    } catch (error) {\n      console.error('生成每日安排时出错:', error);\n      throw new Error('无法生成每日时间安排');\n    }\n  }\n\n  /**\n   * 获取任务建议\n   */\n  getTaskRecommendation(task: Task): string {\n    return this.planningAlgorithm.getTaskRecommendation(task);\n  }\n\n  // ============================================================================\n  // 生活平衡分析相关方法\n  // ============================================================================\n\n  /**\n   * 分析用户的生活平衡状况\n   */\n  async analyzeLifeBalance(userId: string, date?: Date): Promise<BalanceAnalysis> {\n    try {\n      return await this.balanceAlgorithm.analyzeBalance(userId, date);\n    } catch (error) {\n      console.error('分析生活平衡时出错:', error);\n      throw new Error('无法分析生活平衡状况');\n    }\n  }\n\n  /**\n   * 更新今日统计数据\n   */\n  async updateTodayStats(userId: string, category: any, duration: number): Promise<void> {\n    try {\n      await this.balanceAlgorithm.updateTodayStats(userId, category, duration);\n    } catch (error) {\n      console.error('更新统计数据时出错:', error);\n      throw new Error('无法更新统计数据');\n    }\n  }\n\n  /**\n   * 获取分类时间建议\n   */\n  getCategoryTimeRecommendation(category: any, currentRatio: number): string {\n    return this.balanceAlgorithm.getCategoryTimeRecommendation(category, currentRatio);\n  }\n\n  // ============================================================================\n  // 任务修复相关方法\n  // ============================================================================\n\n  /**\n   * 分析推迟的任务\n   */\n  analyzePostponedTasks(tasks: Task[]): PostponedTaskAlert[] {\n    return this.fixAlgorithm.analyzePostponedTasks(tasks);\n  }\n\n  /**\n   * 获取推迟任务统计\n   */\n  getPostponedTasksStats(tasks: Task[]): any {\n    return this.fixAlgorithm.getPostponedTasksStats(tasks);\n  }\n\n  /**\n   * 建议任务重新安排策略\n   */\n  suggestRescheduleStrategy(task: Task): any {\n    return this.fixAlgorithm.suggestRescheduleStrategy(task);\n  }\n\n  /**\n   * 生成修复行动计划\n   */\n  generateActionPlan(alerts: PostponedTaskAlert[]): string[] {\n    return this.fixAlgorithm.generateActionPlan(alerts);\n  }\n\n  // ============================================================================\n  // 时间调整相关方法\n  // ============================================================================\n\n  /**\n   * 处理任务超时调整\n   */\n  async handleTaskOverrun(\n    overrunTask: Task,\n    actualDuration: number,\n    currentSchedule: TimeSlot[],\n    userTimeConfig?: UserTimeConfig\n  ): Promise<AdjustmentResult> {\n    try {\n      const result = this.timeAdjustmentAlgorithm.adjustForOverrun(\n        overrunTask,\n        actualDuration,\n        currentSchedule,\n        userTimeConfig\n      );\n\n      // 如果调整成功，更新统计数据\n      if (result.success) {\n        await this.balanceAlgorithm.updateTodayStats(\n          overrunTask.userId,\n          overrunTask.category,\n          actualDuration\n        );\n      }\n\n      return result;\n    } catch (error) {\n      console.error('处理任务超时时出错:', error);\n      throw new Error('无法处理任务超时调整');\n    }\n  }\n\n  /**\n   * 预测调整影响\n   */\n  predictAdjustmentImpact(\n    overrunMinutes: number,\n    schedule: TimeSlot[],\n    overrunIndex: number\n  ): any {\n    return this.timeAdjustmentAlgorithm.predictAdjustmentImpact(\n      overrunMinutes,\n      schedule,\n      overrunIndex\n    );\n  }\n\n  // ============================================================================\n  // 综合分析和建议\n  // ============================================================================\n\n  /**\n   * 生成综合的每日建议\n   */\n  async generateDailyInsights(\n    userId: string,\n    tasks: Task[],\n    userTimeConfig?: UserTimeConfig\n  ): Promise<{\n    schedule: DailySchedule;\n    balanceAnalysis: BalanceAnalysis;\n    postponedAlerts: PostponedTaskAlert[];\n    recommendations: string[];\n  }> {\n    try {\n      // 并行执行多个分析\n      const [schedule, balanceAnalysis, postponedAlerts] = await Promise.all([\n        this.generateDailySchedule(tasks, userTimeConfig),\n        this.analyzeLifeBalance(userId),\n        Promise.resolve(this.analyzePostponedTasks(tasks))\n      ]);\n\n      // 生成综合建议\n      const recommendations = this.generateComprehensiveRecommendations(\n        schedule,\n        balanceAnalysis,\n        postponedAlerts\n      );\n\n      return {\n        schedule,\n        balanceAnalysis,\n        postponedAlerts,\n        recommendations\n      };\n    } catch (error) {\n      console.error('生成每日洞察时出错:', error);\n      throw new Error('无法生成每日洞察');\n    }\n  }\n\n  /**\n   * 生成综合建议\n   */\n  private generateComprehensiveRecommendations(\n    schedule: DailySchedule,\n    balanceAnalysis: BalanceAnalysis,\n    postponedAlerts: PostponedTaskAlert[]\n  ): string[] {\n    const recommendations: string[] = [];\n\n    // 基于时间安排的建议\n    if (schedule.timeSlots.length === 0) {\n      recommendations.push('📅 今日暂无安排的任务，可以处理一些推迟的任务或进行个人提升');\n    } else if (schedule.estimatedDuration > 480) { // 超过8小时\n      recommendations.push('⏰ 今日安排较满，注意劳逸结合，适当休息');\n    }\n\n    // 基于生活平衡的建议\n    if (balanceAnalysis.balanceScore < 60) {\n      recommendations.push('⚖️ 生活平衡需要调整，' + balanceAnalysis.recommendations[0]);\n    }\n\n    // 基于推迟任务的建议\n    const urgentAlerts = postponedAlerts.filter(alert => alert.severity === 'high');\n    if (urgentAlerts.length > 0) {\n      recommendations.push(`🚨 有 ${urgentAlerts.length} 个高优先级推迟任务需要立即处理`);\n    }\n\n    // 基于任务分布的建议\n    const categoryDistribution = this.analyzeCategoryDistribution(schedule);\n    if (categoryDistribution.work > 0.8) {\n      recommendations.push('💼 今日工作任务较多，记得安排适当的休息时间');\n    } else if (categoryDistribution.entertainment > 0.4) {\n      recommendations.push('🎮 娱乐时间较多，可以考虑增加一些学习或工作任务');\n    }\n\n    return recommendations;\n  }\n\n  /**\n   * 分析任务分类分布\n   */\n  private analyzeCategoryDistribution(schedule: DailySchedule): any {\n    const total = schedule.estimatedDuration;\n    if (total === 0) return { work: 0, improvement: 0, entertainment: 0 };\n\n    const distribution = { work: 0, improvement: 0, entertainment: 0 };\n\n    for (const slot of schedule.timeSlots) {\n      const duration = (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60);\n      distribution[slot.task.category] += duration;\n    }\n\n    return {\n      work: distribution.work / total,\n      improvement: distribution.improvement / total,\n      entertainment: distribution.entertainment / total\n    };\n  }\n\n  // ============================================================================\n  // 算法性能监控\n  // ============================================================================\n\n  /**\n   * 获取算法性能统计\n   */\n  getAlgorithmStats(): {\n    planningCalls: number;\n    balanceCalls: number;\n    fixCalls: number;\n    adjustmentCalls: number;\n  } {\n    // 这里可以添加性能监控逻辑\n    return {\n      planningCalls: 0,\n      balanceCalls: 0,\n      fixCalls: 0,\n      adjustmentCalls: 0\n    };\n  }\n\n  /**\n   * 重置算法统计\n   */\n  resetAlgorithmStats(): void {\n    // 重置性能统计\n    console.log('算法统计已重置');\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAYD;AACA;AACA;AACA;;;;;AAEO,MAAM;IACH,kBAAqC;IACrC,iBAAmC;IACnC,aAA2B;IAC3B,wBAAiD;IAEzD,aAAc;QACZ,IAAI,CAAC,iBAAiB,GAAG,IAAI,4KAAA,CAAA,oBAAiB;QAC9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,2KAAA,CAAA,mBAAgB;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,uKAAA,CAAA,eAAY;QACpC,IAAI,CAAC,uBAAuB,GAAG,IAAI,kLAAA,CAAA,0BAAuB;IAC5D;IAEA,+EAA+E;IAC/E,WAAW;IACX,+EAA+E;IAE/E;;GAEC,GACD,MAAM,sBACJ,KAAa,EACb,cAA+B,EACP;QACxB,IAAI;YACF,iBAAiB;YACjB,MAAM,kBAAkB,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC;YAChE,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,QAC1C,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,MAAM,IAAI;YAGnD,cAAc;YACd,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO;YAErE,uBAAuB;YACvB,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,aAAa,MAAM,CAAC,aAAa,CAAC;YACrD,gBAAgB;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,sBAAsB,IAAU,EAAU;QACxC,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;IACtD;IAEA,+EAA+E;IAC/E,aAAa;IACb,+EAA+E;IAE/E;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAAE,IAAW,EAA4B;QAC9E,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAAE,QAAa,EAAE,QAAgB,EAAiB;QACrF,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,UAAU;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,8BAA8B,QAAa,EAAE,YAAoB,EAAU;QACzE,OAAO,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,UAAU;IACvE;IAEA,+EAA+E;IAC/E,WAAW;IACX,+EAA+E;IAE/E;;GAEC,GACD,sBAAsB,KAAa,EAAwB;QACzD,OAAO,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC;IACjD;IAEA;;GAEC,GACD,uBAAuB,KAAa,EAAO;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC;IAClD;IAEA;;GAEC,GACD,0BAA0B,IAAU,EAAO;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,yBAAyB,CAAC;IACrD;IAEA;;GAEC,GACD,mBAAmB,MAA4B,EAAY;QACzD,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC;IAC9C;IAEA,+EAA+E;IAC/E,WAAW;IACX,+EAA+E;IAE/E;;GAEC,GACD,MAAM,kBACJ,WAAiB,EACjB,cAAsB,EACtB,eAA2B,EAC3B,cAA+B,EACJ;QAC3B,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAC1D,aACA,gBACA,iBACA;YAGF,gBAAgB;YAChB,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAC1C,YAAY,MAAM,EAClB,YAAY,QAAQ,EACpB;YAEJ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,wBACE,cAAsB,EACtB,QAAoB,EACpB,YAAoB,EACf;QACL,OAAO,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CACzD,gBACA,UACA;IAEJ;IAEA,+EAA+E;IAC/E,UAAU;IACV,+EAA+E;IAE/E;;GAEC,GACD,MAAM,sBACJ,MAAc,EACd,KAAa,EACb,cAA+B,EAM9B;QACD,IAAI;YACF,WAAW;YACX,MAAM,CAAC,UAAU,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrE,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBAClC,IAAI,CAAC,kBAAkB,CAAC;gBACxB,QAAQ,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC;aAC5C;YAED,SAAS;YACT,MAAM,kBAAkB,IAAI,CAAC,oCAAoC,CAC/D,UACA,iBACA;YAGF,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,qCACN,QAAuB,EACvB,eAAgC,EAChC,eAAqC,EAC3B;QACV,MAAM,kBAA4B,EAAE;QAEpC,YAAY;QACZ,IAAI,SAAS,SAAS,CAAC,MAAM,KAAK,GAAG;YACnC,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,SAAS,iBAAiB,GAAG,KAAK;YAC3C,gBAAgB,IAAI,CAAC;QACvB;QAEA,YAAY;QACZ,IAAI,gBAAgB,YAAY,GAAG,IAAI;YACrC,gBAAgB,IAAI,CAAC,iBAAiB,gBAAgB,eAAe,CAAC,EAAE;QAC1E;QAEA,YAAY;QACZ,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QACxE,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,gBAAgB,IAAI,CAAC,CAAC,KAAK,EAAE,aAAa,MAAM,CAAC,gBAAgB,CAAC;QACpE;QAEA,YAAY;QACZ,MAAM,uBAAuB,IAAI,CAAC,2BAA2B,CAAC;QAC9D,IAAI,qBAAqB,IAAI,GAAG,KAAK;YACnC,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,qBAAqB,aAAa,GAAG,KAAK;YACnD,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,4BAA4B,QAAuB,EAAO;QAChE,MAAM,QAAQ,SAAS,iBAAiB;QACxC,IAAI,UAAU,GAAG,OAAO;YAAE,MAAM;YAAG,aAAa;YAAG,eAAe;QAAE;QAEpE,MAAM,eAAe;YAAE,MAAM;YAAG,aAAa;YAAG,eAAe;QAAE;QAEjE,KAAK,MAAM,QAAQ,SAAS,SAAS,CAAE;YACrC,MAAM,WAAW,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YACjF,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI;QACtC;QAEA,OAAO;YACL,MAAM,aAAa,IAAI,GAAG;YAC1B,aAAa,aAAa,WAAW,GAAG;YACxC,eAAe,aAAa,aAAa,GAAG;QAC9C;IACF;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,oBAKE;QACA,eAAe;QACf,OAAO;YACL,eAAe;YACf,cAAc;YACd,UAAU;YACV,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,sBAA4B;QAC1B,SAAS;QACT,QAAQ,GAAG,CAAC;IACd;AACF", "debugId": null}}, {"offset": {"line": 2005, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/services/PlanningService.ts"], "sourcesContent": ["/**\n * 智能规划服务\n * 提供高级的规划服务接口，封装算法复杂性\n */\n\nimport {\n  Task,\n  DailySchedule,\n  BalanceAnalysis,\n  PostponedTaskAlert,\n  AdjustmentResult,\n  UserTimeConfig,\n  TimeSlot,\n  ApiResponse,\n  createAppError,\n  ERROR_CODES\n} from '@/shared';\n\nimport { AlgorithmCoordinator } from '../coordinators/AlgorithmCoordinator';\n\nexport class PlanningService {\n  private algorithmCoordinator: AlgorithmCoordinator;\n\n  constructor() {\n    this.algorithmCoordinator = new AlgorithmCoordinator();\n  }\n\n  // ============================================================================\n  // 每日规划服务\n  // ============================================================================\n\n  /**\n   * 生成智能每日规划\n   */\n  async generateDailyPlan(\n    userId: string,\n    tasks: Task[],\n    userTimeConfig?: UserTimeConfig\n  ): Promise<ApiResponse<{\n    schedule: DailySchedule;\n    insights: any;\n    recommendations: string[];\n  }>> {\n    try {\n      // 验证输入\n      if (!userId) {\n        return {\n          success: false,\n          error: '用户ID不能为空',\n          data: null as any\n        };\n      }\n\n      if (!Array.isArray(tasks)) {\n        return {\n          success: false,\n          error: '任务列表格式不正确',\n          data: null as any\n        };\n      }\n\n      // 生成每日洞察\n      const insights = await this.algorithmCoordinator.generateDailyInsights(\n        userId,\n        tasks,\n        userTimeConfig\n      );\n\n      return {\n        success: true,\n        data: {\n          schedule: insights.schedule,\n          insights: {\n            balanceAnalysis: insights.balanceAnalysis,\n            postponedAlerts: insights.postponedAlerts,\n            stats: this.algorithmCoordinator.getPostponedTasksStats(tasks)\n          },\n          recommendations: insights.recommendations\n        }\n      };\n    } catch (error) {\n      console.error('生成每日规划失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '生成每日规划时发生未知错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 快速生成时间安排（简化版）\n   */\n  async generateQuickSchedule(\n    tasks: Task[],\n    workHours?: { start: string; end: string }\n  ): Promise<ApiResponse<DailySchedule>> {\n    try {\n      const schedule = this.algorithmCoordinator.planningAlgorithm.generateTimeSlots(\n        tasks.map(task => ({ ...task, score: 0, quadrant: 1 as any })),\n        workHours || { start: '09:00', end: '18:00' }\n      );\n\n      return {\n        success: true,\n        data: {\n          date: new Date(),\n          timeSlots: schedule,\n          totalTasks: tasks.length,\n          estimatedDuration: schedule.reduce((sum, slot) => \n            sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0\n          )\n        }\n      };\n    } catch (error) {\n      console.error('生成快速安排失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '生成快速安排时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  // ============================================================================\n  // 生活平衡服务\n  // ============================================================================\n\n  /**\n   * 获取生活平衡分析\n   */\n  async getBalanceAnalysis(userId: string): Promise<ApiResponse<BalanceAnalysis>> {\n    try {\n      if (!userId) {\n        return {\n          success: false,\n          error: '用户ID不能为空',\n          data: null as any\n        };\n      }\n\n      const analysis = await this.algorithmCoordinator.analyzeLifeBalance(userId);\n\n      return {\n        success: true,\n        data: analysis\n      };\n    } catch (error) {\n      console.error('获取生活平衡分析失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取生活平衡分析时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 更新活动统计\n   */\n  async updateActivityStats(\n    userId: string,\n    category: 'work' | 'improvement' | 'entertainment',\n    duration: number\n  ): Promise<ApiResponse<void>> {\n    try {\n      if (!userId || !category || duration <= 0) {\n        return {\n          success: false,\n          error: '参数不完整或无效',\n          data: undefined\n        };\n      }\n\n      await this.algorithmCoordinator.updateTodayStats(userId, category, duration);\n\n      return {\n        success: true,\n        data: undefined\n      };\n    } catch (error) {\n      console.error('更新活动统计失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '更新活动统计时发生错误',\n        data: undefined\n      };\n    }\n  }\n\n  // ============================================================================\n  // 任务修复服务\n  // ============================================================================\n\n  /**\n   * 获取推迟任务分析\n   */\n  async getPostponedTasksAnalysis(tasks: Task[]): Promise<ApiResponse<{\n    alerts: PostponedTaskAlert[];\n    stats: any;\n    actionPlan: string[];\n  }>> {\n    try {\n      if (!Array.isArray(tasks)) {\n        return {\n          success: false,\n          error: '任务列表格式不正确',\n          data: null as any\n        };\n      }\n\n      const alerts = this.algorithmCoordinator.analyzePostponedTasks(tasks);\n      const stats = this.algorithmCoordinator.getPostponedTasksStats(tasks);\n      const actionPlan = this.algorithmCoordinator.generateActionPlan(alerts);\n\n      return {\n        success: true,\n        data: {\n          alerts,\n          stats,\n          actionPlan\n        }\n      };\n    } catch (error) {\n      console.error('获取推迟任务分析失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取推迟任务分析时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 获取任务重新安排建议\n   */\n  async getTaskRescheduleAdvice(task: Task): Promise<ApiResponse<any>> {\n    try {\n      if (!task || !task.id) {\n        return {\n          success: false,\n          error: '任务信息不完整',\n          data: null as any\n        };\n      }\n\n      const advice = this.algorithmCoordinator.suggestRescheduleStrategy(task);\n\n      return {\n        success: true,\n        data: advice\n      };\n    } catch (error) {\n      console.error('获取重新安排建议失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取重新安排建议时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  // ============================================================================\n  // 时间调整服务\n  // ============================================================================\n\n  /**\n   * 处理任务超时\n   */\n  async handleTaskOverrun(\n    overrunTask: Task,\n    actualDuration: number,\n    currentSchedule: TimeSlot[],\n    userTimeConfig?: UserTimeConfig\n  ): Promise<ApiResponse<AdjustmentResult>> {\n    try {\n      // 验证输入\n      if (!overrunTask || !overrunTask.id) {\n        return {\n          success: false,\n          error: '超时任务信息不完整',\n          data: null as any\n        };\n      }\n\n      if (actualDuration <= 0) {\n        return {\n          success: false,\n          error: '实际持续时间必须大于0',\n          data: null as any\n        };\n      }\n\n      if (!Array.isArray(currentSchedule)) {\n        return {\n          success: false,\n          error: '当前安排格式不正确',\n          data: null as any\n        };\n      }\n\n      const result = await this.algorithmCoordinator.handleTaskOverrun(\n        overrunTask,\n        actualDuration,\n        currentSchedule,\n        userTimeConfig\n      );\n\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      console.error('处理任务超时失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '处理任务超时时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 预测时间调整影响\n   */\n  async predictOverrunImpact(\n    overrunMinutes: number,\n    schedule: TimeSlot[],\n    overrunTaskIndex: number\n  ): Promise<ApiResponse<any>> {\n    try {\n      if (overrunMinutes <= 0) {\n        return {\n          success: false,\n          error: '超时分钟数必须大于0',\n          data: null as any\n        };\n      }\n\n      if (!Array.isArray(schedule) || overrunTaskIndex < 0 || overrunTaskIndex >= schedule.length) {\n        return {\n          success: false,\n          error: '安排信息或任务索引无效',\n          data: null as any\n        };\n      }\n\n      const impact = this.algorithmCoordinator.predictAdjustmentImpact(\n        overrunMinutes,\n        schedule,\n        overrunTaskIndex\n      );\n\n      return {\n        success: true,\n        data: impact\n      };\n    } catch (error) {\n      console.error('预测调整影响失败:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '预测调整影响时发生错误',\n        data: null as any\n      };\n    }\n  }\n\n  // ============================================================================\n  // 工具方法\n  // ============================================================================\n\n  /**\n   * 获取任务建议\n   */\n  getTaskRecommendation(task: Task): string {\n    try {\n      return this.algorithmCoordinator.getTaskRecommendation(task);\n    } catch (error) {\n      console.error('获取任务建议失败:', error);\n      return '暂无建议';\n    }\n  }\n\n  /**\n   * 获取分类时间建议\n   */\n  getCategoryAdvice(category: 'work' | 'improvement' | 'entertainment', ratio: number): string {\n    try {\n      return this.algorithmCoordinator.getCategoryTimeRecommendation(category, ratio);\n    } catch (error) {\n      console.error('获取分类建议失败:', error);\n      return '暂无建议';\n    }\n  }\n\n  /**\n   * 获取服务状态\n   */\n  getServiceStatus(): {\n    isHealthy: boolean;\n    algorithmStats: any;\n    lastUpdate: Date;\n  } {\n    try {\n      return {\n        isHealthy: true,\n        algorithmStats: this.algorithmCoordinator.getAlgorithmStats(),\n        lastUpdate: new Date()\n      };\n    } catch (error) {\n      console.error('获取服务状态失败:', error);\n      return {\n        isHealthy: false,\n        algorithmStats: null,\n        lastUpdate: new Date()\n      };\n    }\n  }\n\n  /**\n   * 重置服务统计\n   */\n  resetServiceStats(): void {\n    try {\n      this.algorithmCoordinator.resetAlgorithmStats();\n    } catch (error) {\n      console.error('重置服务统计失败:', error);\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAeD;;AAEO,MAAM;IACH,qBAA2C;IAEnD,aAAc;QACZ,IAAI,CAAC,oBAAoB,GAAG,IAAI,iLAAA,CAAA,uBAAoB;IACtD;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,MAAM,kBACJ,MAAc,EACd,KAAa,EACb,cAA+B,EAK7B;QACF,IAAI;YACF,OAAO;YACP,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,SAAS;YACT,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CACpE,QACA,OACA;YAGF,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,UAAU,SAAS,QAAQ;oBAC3B,UAAU;wBACR,iBAAiB,SAAS,eAAe;wBACzC,iBAAiB,SAAS,eAAe;wBACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC;oBAC1D;oBACA,iBAAiB,SAAS,eAAe;gBAC3C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,KAAa,EACb,SAA0C,EACL;QACrC,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,iBAAiB,CAC5E,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;oBAAG,UAAU;gBAAS,CAAC,IAC5D,aAAa;gBAAE,OAAO;gBAAS,KAAK;YAAQ;YAG9C,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,MAAM,IAAI;oBACV,WAAW;oBACX,YAAY,MAAM,MAAM;oBACxB,mBAAmB,SAAS,MAAM,CAAC,CAAC,KAAK,OACvC,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG;gBAE7E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAAyC;QAC9E,IAAI;YACF,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;YAEpE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,QAAkD,EAClD,QAAgB,EACY;QAC5B,IAAI;YACF,IAAI,CAAC,UAAU,CAAC,YAAY,YAAY,GAAG;gBACzC,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,QAAQ,UAAU;YAEnE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,MAAM,0BAA0B,KAAa,EAIzC;QACF,IAAI;YACF,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC;YAC/D,MAAM,QAAQ,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC;YAC/D,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;YAEhE,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB,IAAU,EAA6B;QACnE,IAAI;YACF,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE;gBACrB,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC;YAEnE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,MAAM,kBACJ,WAAiB,EACjB,cAAsB,EACtB,eAA2B,EAC3B,cAA+B,EACS;QACxC,IAAI;YACF,OAAO;YACP,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE;gBACnC,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,kBAAkB,GAAG;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,MAAM,OAAO,CAAC,kBAAkB;gBACnC,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,SAAS,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAC9D,aACA,gBACA,iBACA;YAGF,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,cAAsB,EACtB,QAAoB,EACpB,gBAAwB,EACG;QAC3B,IAAI;YACF,IAAI,kBAAkB,GAAG;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,mBAAmB,KAAK,oBAAoB,SAAS,MAAM,EAAE;gBAC3F,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAC9D,gBACA,UACA;YAGF,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA,+EAA+E;IAC/E,OAAO;IACP,+EAA+E;IAE/E;;GAEC,GACD,sBAAsB,IAAU,EAAU;QACxC,IAAI;YACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,kBAAkB,QAAkD,EAAE,KAAa,EAAU;QAC3F,IAAI;YACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,UAAU;QAC3E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAIE;QACA,IAAI;YACF,OAAO;gBACL,WAAW;gBACX,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB;gBAC3D,YAAY,IAAI;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBACL,WAAW;gBACX,gBAAgB;gBAChB,YAAY,IAAI;YAClB;QACF;IACF;IAEA;;GAEC,GACD,oBAA0B;QACxB,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC,mBAAmB;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/intelligent-planning/index.ts"], "sourcesContent": ["/**\n * 智能规划域统一导出\n * 提供智能规划相关的所有功能\n */\n\n// ============================================================================\n// 算法导出\n// ============================================================================\n\nexport { PlanningAlgorithm } from './algorithms/PlanningAlgorithm';\nexport { BalanceAlgorithm } from './algorithms/BalanceAlgorithm';\nexport { FixAlgorithm } from './algorithms/FixAlgorithm';\nexport { TimeAdjustmentAlgorithm } from './algorithms/TimeAdjustmentAlgorithm';\n\n// ============================================================================\n// 协调器导出\n// ============================================================================\n\nexport { AlgorithmCoordinator } from './coordinators/AlgorithmCoordinator';\n\n// ============================================================================\n// 服务导出\n// ============================================================================\n\nexport { PlanningService } from './services/PlanningService';\n\n// ============================================================================\n// 便捷实例创建\n// ============================================================================\n\nimport { PlanningService } from './services/PlanningService';\nimport { AlgorithmCoordinator } from './coordinators/AlgorithmCoordinator';\nimport { PlanningAlgorithm } from './algorithms/PlanningAlgorithm';\nimport { BalanceAlgorithm } from './algorithms/BalanceAlgorithm';\nimport { FixAlgorithm } from './algorithms/FixAlgorithm';\nimport { TimeAdjustmentAlgorithm } from './algorithms/TimeAdjustmentAlgorithm';\n\n/**\n * 创建规划服务实例\n */\nexport function createPlanningService(): PlanningService {\n  return new PlanningService();\n}\n\n/**\n * 创建算法协调器实例\n */\nexport function createAlgorithmCoordinator(): AlgorithmCoordinator {\n  return new AlgorithmCoordinator();\n}\n\n/**\n * 创建单独的算法实例\n */\nexport function createPlanningAlgorithm(): PlanningAlgorithm {\n  return new PlanningAlgorithm();\n}\n\nexport function createBalanceAlgorithm(): BalanceAlgorithm {\n  return new BalanceAlgorithm();\n}\n\nexport function createFixAlgorithm(): FixAlgorithm {\n  return new FixAlgorithm();\n}\n\nexport function createTimeAdjustmentAlgorithm(): TimeAdjustmentAlgorithm {\n  return new TimeAdjustmentAlgorithm();\n}\n\n// ============================================================================\n// 默认实例（单例模式）\n// ============================================================================\n\nlet defaultPlanningService: PlanningService | null = null;\n\n/**\n * 获取默认的规划服务实例（单例）\n */\nexport function getDefaultPlanningService(): PlanningService {\n  if (!defaultPlanningService) {\n    defaultPlanningService = new PlanningService();\n  }\n  return defaultPlanningService;\n}\n\n/**\n * 重置默认实例\n */\nexport function resetDefaultPlanningService(): void {\n  defaultPlanningService = null;\n}\n\n// ============================================================================\n// 类型重新导出（从 shared 模块）\n// ============================================================================\n\nexport type {\n  Task,\n  DailySchedule,\n  TimeSlot,\n  ScoredTask,\n  BalanceAnalysis,\n  PostponedTaskAlert,\n  AdjustmentResult,\n  UserTimeConfig,\n  CategoryRatios,\n  WeeklyStats,\n  DailyStats\n} from '@/shared';\n\n// ============================================================================\n// 工具函数\n// ============================================================================\n\n/**\n * 验证规划服务的健康状态\n */\nexport async function validatePlanningServiceHealth(): Promise<{\n  isHealthy: boolean;\n  errors: string[];\n  timestamp: Date;\n}> {\n  const errors: string[] = [];\n  let isHealthy = true;\n\n  try {\n    const service = getDefaultPlanningService();\n    const status = service.getServiceStatus();\n    \n    if (!status.isHealthy) {\n      errors.push('规划服务状态异常');\n      isHealthy = false;\n    }\n  } catch (error) {\n    errors.push(`规划服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    isHealthy = false;\n  }\n\n  return {\n    isHealthy,\n    errors,\n    timestamp: new Date()\n  };\n}\n\n/**\n * 获取智能规划域的功能清单\n */\nexport function getPlanningDomainFeatures(): {\n  algorithms: string[];\n  services: string[];\n  capabilities: string[];\n} {\n  return {\n    algorithms: [\n      'PlanningAlgorithm - 智能时间规划',\n      'BalanceAlgorithm - 生活平衡分析',\n      'FixAlgorithm - 推迟任务修复',\n      'TimeAdjustmentAlgorithm - 动态时间调整'\n    ],\n    services: [\n      'PlanningService - 统一规划服务接口',\n      'AlgorithmCoordinator - 算法协调管理'\n    ],\n    capabilities: [\n      '基于四象限的智能任务排序',\n      '考虑任务类型的时间分配',\n      '生活平衡状况分析和建议',\n      '推迟任务检测和修复建议',\n      '任务超时的动态时间调整',\n      '综合的每日规划洞察',\n      '个性化的时间管理建议'\n    ]\n  };\n}\n\n/**\n * 获取算法性能基准测试\n */\nexport async function runAlgorithmBenchmark(): Promise<{\n  planningTime: number;\n  balanceTime: number;\n  fixTime: number;\n  adjustmentTime: number;\n  totalTime: number;\n}> {\n  const coordinator = createAlgorithmCoordinator();\n  \n  // 模拟测试数据\n  const mockTasks: Task[] = [\n    {\n      id: '1',\n      userId: 'test',\n      title: '测试任务1',\n      category: 'work',\n      importance: 4,\n      urgency: 3,\n      deadline: new Date(),\n      estimatedDuration: 60,\n      status: 'pending',\n      postponeCount: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    }\n  ];\n\n  const startTime = Date.now();\n  \n  // 测试规划算法\n  const planningStart = Date.now();\n  await coordinator.generateDailySchedule(mockTasks);\n  const planningTime = Date.now() - planningStart;\n\n  // 测试平衡算法\n  const balanceStart = Date.now();\n  await coordinator.analyzeLifeBalance('test');\n  const balanceTime = Date.now() - balanceStart;\n\n  // 测试修复算法\n  const fixStart = Date.now();\n  coordinator.analyzePostponedTasks(mockTasks);\n  const fixTime = Date.now() - fixStart;\n\n  // 测试调整算法\n  const adjustmentStart = Date.now();\n  coordinator.predictAdjustmentImpact(30, [], 0);\n  const adjustmentTime = Date.now() - adjustmentStart;\n\n  const totalTime = Date.now() - startTime;\n\n  return {\n    planningTime,\n    balanceTime,\n    fixTime,\n    adjustmentTime,\n    totalTime\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;;;;;;;;;;;;;;AAE/E;AACA;AACA;AACA;AAEA,+EAA+E;AAC/E,QAAQ;AACR,+EAA+E;AAE/E;AAEA,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E;;;;;;;;;;;;;AAgBO,SAAS;IACd,OAAO,IAAI,wKAAA,CAAA,kBAAe;AAC5B;AAKO,SAAS;IACd,OAAO,IAAI,iLAAA,CAAA,uBAAoB;AACjC;AAKO,SAAS;IACd,OAAO,IAAI,4KAAA,CAAA,oBAAiB;AAC9B;AAEO,SAAS;IACd,OAAO,IAAI,2KAAA,CAAA,mBAAgB;AAC7B;AAEO,SAAS;IACd,OAAO,IAAI,uKAAA,CAAA,eAAY;AACzB;AAEO,SAAS;IACd,OAAO,IAAI,kLAAA,CAAA,0BAAuB;AACpC;AAEA,+EAA+E;AAC/E,aAAa;AACb,+EAA+E;AAE/E,IAAI,yBAAiD;AAK9C,SAAS;IACd,IAAI,CAAC,wBAAwB;QAC3B,yBAAyB,IAAI,wKAAA,CAAA,kBAAe;IAC9C;IACA,OAAO;AACT;AAKO,SAAS;IACd,yBAAyB;AAC3B;AA2BO,eAAe;IAKpB,MAAM,SAAmB,EAAE;IAC3B,IAAI,YAAY;IAEhB,IAAI;QACF,MAAM,UAAU;QAChB,MAAM,SAAS,QAAQ,gBAAgB;QAEvC,IAAI,CAAC,OAAO,SAAS,EAAE;YACrB,OAAO,IAAI,CAAC;YACZ,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC3E,YAAY;IACd;IAEA,OAAO;QACL;QACA;QACA,WAAW,IAAI;IACjB;AACF;AAKO,SAAS;IAKd,OAAO;QACL,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,eAAe;IAOpB,MAAM,cAAc;IAEpB,SAAS;IACT,MAAM,YAAoB;QACxB;YACE,IAAI;YACJ,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU,IAAI;YACd,mBAAmB;YACnB,QAAQ;YACR,eAAe;YACf,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IAED,MAAM,YAAY,KAAK,GAAG;IAE1B,SAAS;IACT,MAAM,gBAAgB,KAAK,GAAG;IAC9B,MAAM,YAAY,qBAAqB,CAAC;IACxC,MAAM,eAAe,KAAK,GAAG,KAAK;IAElC,SAAS;IACT,MAAM,eAAe,KAAK,GAAG;IAC7B,MAAM,YAAY,kBAAkB,CAAC;IACrC,MAAM,cAAc,KAAK,GAAG,KAAK;IAEjC,SAAS;IACT,MAAM,WAAW,KAAK,GAAG;IACzB,YAAY,qBAAqB,CAAC;IAClC,MAAM,UAAU,KAAK,GAAG,KAAK;IAE7B,SAAS;IACT,MAAM,kBAAkB,KAAK,GAAG;IAChC,YAAY,uBAAuB,CAAC,IAAI,EAAE,EAAE;IAC5C,MAAM,iBAAiB,KAAK,GAAG,KAAK;IAEpC,MAAM,YAAY,KAAK,GAAG,KAAK;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/task-management/models/Task.ts"], "sourcesContent": ["/**\n * 任务模型\n * 定义任务的数据结构和相关类型\n */\n\nimport {\n  Task,\n  TaskCategory,\n  Priority,\n  TaskStatus,\n  Quadrant\n} from '@/shared';\n\n/**\n * 数据库任务记录类型\n * 用于与数据库交互\n */\nexport interface TaskRecord {\n  id: string;\n  user_id: string;\n  title: string;\n  description?: string;\n  category: string;\n  importance: number;\n  urgency: number;\n  deadline: string;\n  estimated_duration: number;\n  status: string;\n  postpone_count: number;\n  created_at: string;\n  updated_at: string;\n}\n\n/**\n * 任务创建请求类型\n */\nexport interface CreateTaskRequest {\n  userId: string;\n  title: string;\n  description?: string;\n  category: TaskCategory;\n  importance: Priority;\n  urgency: Priority;\n  deadline: Date;\n  estimatedDuration: number;\n  status?: TaskStatus;\n  postponeCount?: number;\n}\n\n/**\n * 任务更新请求类型\n */\nexport interface UpdateTaskRequest {\n  id: string;\n  title?: string;\n  description?: string;\n  category?: TaskCategory;\n  importance?: Priority;\n  urgency?: Priority;\n  deadline?: Date;\n  estimatedDuration?: number;\n  status?: TaskStatus;\n  postponeCount?: number;\n}\n\n/**\n * 任务完成请求类型\n */\nexport interface CompleteTaskRequest {\n  id: string;\n  actualDuration?: number;\n  satisfactionScore?: number;\n}\n\n/**\n * 任务推迟请求类型\n */\nexport interface PostponeTaskRequest {\n  id: string;\n  reason?: string;\n  newDeadline?: Date;\n}\n\n/**\n * 任务过滤条件类型\n */\nexport interface TaskFilter {\n  userId: string;\n  status?: TaskStatus | TaskStatus[];\n  category?: TaskCategory | TaskCategory[];\n  quadrant?: Quadrant | Quadrant[];\n  startDate?: Date;\n  endDate?: Date;\n  searchTerm?: string;\n}\n\n/**\n * 任务排序选项类型\n */\nexport type TaskSortOption = \n  | 'deadline' \n  | 'importance' \n  | 'urgency' \n  | 'createdAt' \n  | 'estimatedDuration' \n  | 'quadrant';\n\n/**\n * 任务排序方向类型\n */\nexport type SortDirection = 'asc' | 'desc';\n\n/**\n * 任务排序配置类型\n */\nexport interface TaskSortConfig {\n  sortBy: TaskSortOption;\n  direction: SortDirection;\n}\n\n/**\n * 任务分页选项类型\n */\nexport interface TaskPaginationOptions {\n  page: number;\n  pageSize: number;\n}\n\n/**\n * 任务分页结果类型\n */\nexport interface TaskPaginationResult {\n  tasks: Task[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n/**\n * 任务统计类型\n */\nexport interface TaskStatistics {\n  total: number;\n  byStatus: Record<TaskStatus, number>;\n  byCategory: Record<TaskCategory, number>;\n  byQuadrant: Record<Quadrant, number>;\n  overdue: number;\n  dueSoon: number;\n  averageCompletionTime: number;\n}\n\n/**\n * 任务完成记录类型\n */\nexport interface TaskCompletion {\n  id: string;\n  taskId: string;\n  userId: string;\n  completedAt: Date;\n  actualDuration: number;\n  estimatedDuration: number;\n  efficiencyRatio: number;\n  satisfactionScore?: number;\n}\n\n/**\n * 将数据库记录转换为任务对象\n */\nexport function mapRecordToTask(record: TaskRecord): Task {\n  return {\n    id: record.id,\n    userId: record.user_id,\n    title: record.title,\n    description: record.description,\n    category: record.category as TaskCategory,\n    importance: record.importance as Priority,\n    urgency: record.urgency as Priority,\n    deadline: new Date(record.deadline),\n    estimatedDuration: record.estimated_duration,\n    status: record.status as TaskStatus,\n    postponeCount: record.postpone_count,\n    createdAt: new Date(record.created_at),\n    updatedAt: new Date(record.updated_at)\n  };\n}\n\n/**\n * 将任务对象转换为数据库记录\n */\nexport function mapTaskToRecord(task: Task | CreateTaskRequest): Omit<TaskRecord, 'id' | 'created_at' | 'updated_at'> {\n  return {\n    user_id: task.userId,\n    title: task.title,\n    description: task.description || '',\n    category: task.category,\n    importance: task.importance,\n    urgency: task.urgency,\n    deadline: task.deadline.toISOString(),\n    estimated_duration: task.estimatedDuration,\n    status: (task as Task).status || 'pending',\n    postpone_count: (task as Task).postponeCount || 0\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAsKM,SAAS,gBAAgB,MAAkB;IAChD,OAAO;QACL,IAAI,OAAO,EAAE;QACb,QAAQ,OAAO,OAAO;QACtB,OAAO,OAAO,KAAK;QACnB,aAAa,OAAO,WAAW;QAC/B,UAAU,OAAO,QAAQ;QACzB,YAAY,OAAO,UAAU;QAC7B,SAAS,OAAO,OAAO;QACvB,UAAU,IAAI,KAAK,OAAO,QAAQ;QAClC,mBAAmB,OAAO,kBAAkB;QAC5C,QAAQ,OAAO,MAAM;QACrB,eAAe,OAAO,cAAc;QACpC,WAAW,IAAI,KAAK,OAAO,UAAU;QACrC,WAAW,IAAI,KAAK,OAAO,UAAU;IACvC;AACF;AAKO,SAAS,gBAAgB,IAA8B;IAC5D,OAAO;QACL,SAAS,KAAK,MAAM;QACpB,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,WAAW,IAAI;QACjC,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,SAAS,KAAK,OAAO;QACrB,UAAU,KAAK,QAAQ,CAAC,WAAW;QACnC,oBAAoB,KAAK,iBAAiB;QAC1C,QAAQ,AAAC,KAAc,MAAM,IAAI;QACjC,gBAAgB,AAAC,KAAc,aAAa,IAAI;IAClD;AACF", "debugId": null}}, {"offset": {"line": 2563, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/task-management/repositories/TaskRepository.ts"], "sourcesContent": ["/**\n * 任务仓储层\n * 负责任务数据的持久化操作\n */\n\nimport { supabase } from '@/lib/supabase';\nimport {\n  Task,\n  TaskCategory,\n  TaskStatus,\n  ApiResponse,\n  createAppError,\n  ERROR_CODES\n} from '@/shared';\n\nimport {\n  TaskRecord,\n  CreateTaskRequest,\n  UpdateTaskRequest,\n  TaskFilter,\n  TaskSortConfig,\n  TaskPaginationOptions,\n  TaskPaginationResult,\n  mapRecordToTask,\n  mapTaskToRecord\n} from '../models/Task';\n\nexport class TaskRepository {\n\n  /**\n   * 获取用户的所有任务\n   */\n  async findByUserId(userId: string): Promise<ApiResponse<Task[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .eq('user_id', userId)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        return {\n          success: false,\n          error: `获取任务失败: ${error.message}`,\n          data: []\n        };\n      }\n\n      const tasks = data.map(mapRecordToTask);\n\n      return {\n        success: true,\n        data: tasks\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取任务时发生未知错误',\n        data: []\n      };\n    }\n  }\n\n  /**\n   * 根据ID获取任务\n   */\n  async findById(id: string): Promise<ApiResponse<Task | null>> {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .eq('id', id)\n        .single();\n\n      if (error) {\n        if (error.code === 'PGRST116') {\n          return {\n            success: true,\n            data: null\n          };\n        }\n        return {\n          success: false,\n          error: `获取任务失败: ${error.message}`,\n          data: null\n        };\n      }\n\n      const task = mapRecordToTask(data);\n\n      return {\n        success: true,\n        data: task\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取任务时发生未知错误',\n        data: null\n      };\n    }\n  }\n\n  /**\n   * 创建新任务\n   */\n  async create(taskData: CreateTaskRequest): Promise<ApiResponse<Task>> {\n    try {\n      const record = mapTaskToRecord(taskData);\n\n      const { data, error } = await supabase\n        .from('tasks')\n        .insert(record)\n        .select()\n        .single();\n\n      if (error) {\n        return {\n          success: false,\n          error: `创建任务失败: ${error.message}`,\n          data: null as any\n        };\n      }\n\n      const task = mapRecordToTask(data);\n\n      return {\n        success: true,\n        data: task\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '创建任务时发生未知错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 更新任务\n   */\n  async update(updateData: UpdateTaskRequest): Promise<ApiResponse<Task>> {\n    try {\n      const { id, ...updates } = updateData;\n      \n      // 构建更新数据\n      const updateRecord: Partial<TaskRecord> = {};\n      \n      if (updates.title !== undefined) updateRecord.title = updates.title;\n      if (updates.description !== undefined) updateRecord.description = updates.description;\n      if (updates.category !== undefined) updateRecord.category = updates.category;\n      if (updates.importance !== undefined) updateRecord.importance = updates.importance;\n      if (updates.urgency !== undefined) updateRecord.urgency = updates.urgency;\n      if (updates.deadline !== undefined) updateRecord.deadline = updates.deadline.toISOString();\n      if (updates.estimatedDuration !== undefined) updateRecord.estimated_duration = updates.estimatedDuration;\n      if (updates.status !== undefined) updateRecord.status = updates.status;\n      if (updates.postponeCount !== undefined) updateRecord.postpone_count = updates.postponeCount;\n\n      const { data, error } = await supabase\n        .from('tasks')\n        .update(updateRecord)\n        .eq('id', id)\n        .select()\n        .single();\n\n      if (error) {\n        return {\n          success: false,\n          error: `更新任务失败: ${error.message}`,\n          data: null as any\n        };\n      }\n\n      const task = mapRecordToTask(data);\n\n      return {\n        success: true,\n        data: task\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '更新任务时发生未知错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 删除任务\n   */\n  async delete(id: string): Promise<ApiResponse<void>> {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .delete()\n        .eq('id', id);\n\n      if (error) {\n        return {\n          success: false,\n          error: `删除任务失败: ${error.message}`,\n          data: undefined\n        };\n      }\n\n      return {\n        success: true,\n        data: undefined\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '删除任务时发生未知错误',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * 根据条件过滤任务\n   */\n  async findByFilter(filter: TaskFilter): Promise<ApiResponse<Task[]>> {\n    try {\n      let query = supabase\n        .from('tasks')\n        .select('*')\n        .eq('user_id', filter.userId);\n\n      // 应用状态过滤\n      if (filter.status) {\n        if (Array.isArray(filter.status)) {\n          query = query.in('status', filter.status);\n        } else {\n          query = query.eq('status', filter.status);\n        }\n      }\n\n      // 应用分类过滤\n      if (filter.category) {\n        if (Array.isArray(filter.category)) {\n          query = query.in('category', filter.category);\n        } else {\n          query = query.eq('category', filter.category);\n        }\n      }\n\n      // 应用日期范围过滤\n      if (filter.startDate) {\n        query = query.gte('deadline', filter.startDate.toISOString());\n      }\n      if (filter.endDate) {\n        query = query.lte('deadline', filter.endDate.toISOString());\n      }\n\n      // 应用搜索条件\n      if (filter.searchTerm) {\n        query = query.or(`title.ilike.%${filter.searchTerm}%,description.ilike.%${filter.searchTerm}%`);\n      }\n\n      const { data, error } = await query.order('created_at', { ascending: false });\n\n      if (error) {\n        return {\n          success: false,\n          error: `过滤任务失败: ${error.message}`,\n          data: []\n        };\n      }\n\n      const tasks = data.map(mapRecordToTask);\n\n      return {\n        success: true,\n        data: tasks\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '过滤任务时发生未知错误',\n        data: []\n      };\n    }\n  }\n\n  /**\n   * 分页获取任务\n   */\n  async findWithPagination(\n    userId: string,\n    pagination: TaskPaginationOptions,\n    sort?: TaskSortConfig\n  ): Promise<ApiResponse<TaskPaginationResult>> {\n    try {\n      const { page, pageSize } = pagination;\n      const offset = (page - 1) * pageSize;\n\n      let query = supabase\n        .from('tasks')\n        .select('*', { count: 'exact' })\n        .eq('user_id', userId);\n\n      // 应用排序\n      if (sort) {\n        const column = this.mapSortOptionToColumn(sort.sortBy);\n        query = query.order(column, { ascending: sort.direction === 'asc' });\n      } else {\n        query = query.order('created_at', { ascending: false });\n      }\n\n      // 应用分页\n      query = query.range(offset, offset + pageSize - 1);\n\n      const { data, error, count } = await query;\n\n      if (error) {\n        return {\n          success: false,\n          error: `分页获取任务失败: ${error.message}`,\n          data: null as any\n        };\n      }\n\n      const tasks = data.map(mapRecordToTask);\n      const total = count || 0;\n      const totalPages = Math.ceil(total / pageSize);\n\n      return {\n        success: true,\n        data: {\n          tasks,\n          total,\n          page,\n          pageSize,\n          totalPages\n        }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '分页获取任务时发生未知错误',\n        data: null as any\n      };\n    }\n  }\n\n  /**\n   * 批量更新任务状态\n   */\n  async batchUpdateStatus(taskIds: string[], status: TaskStatus): Promise<ApiResponse<void>> {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .update({ status })\n        .in('id', taskIds);\n\n      if (error) {\n        return {\n          success: false,\n          error: `批量更新任务状态失败: ${error.message}`,\n          data: undefined\n        };\n      }\n\n      return {\n        success: true,\n        data: undefined\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '批量更新任务状态时发生未知错误',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * 映射排序选项到数据库列名\n   */\n  private mapSortOptionToColumn(sortOption: string): string {\n    const mapping: Record<string, string> = {\n      deadline: 'deadline',\n      importance: 'importance',\n      urgency: 'urgency',\n      createdAt: 'created_at',\n      estimatedDuration: 'estimated_duration'\n    };\n\n    return mapping[sortOption] || 'created_at';\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAUA;;;AAYO,MAAM;IAEX;;GAEC,GACD,MAAM,aAAa,MAAc,EAAgC;QAC/D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM,EAAE;gBACV;YACF;YAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,sJAAA,CAAA,kBAAe;YAEtC,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM,EAAE;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,SAAS,EAAU,EAAqC;QAC5D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO;gBACT,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;gBACF;gBACA,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM;gBACR;YACF;YAEA,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD,EAAE;YAE7B,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,OAAO,QAA2B,EAA8B;QACpE,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD,EAAE;YAE/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM;gBACR;YACF;YAEA,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD,EAAE;YAE7B,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,OAAO,UAA6B,EAA8B;QACtE,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,GAAG;YAE3B,SAAS;YACT,MAAM,eAAoC,CAAC;YAE3C,IAAI,QAAQ,KAAK,KAAK,WAAW,aAAa,KAAK,GAAG,QAAQ,KAAK;YACnE,IAAI,QAAQ,WAAW,KAAK,WAAW,aAAa,WAAW,GAAG,QAAQ,WAAW;YACrF,IAAI,QAAQ,QAAQ,KAAK,WAAW,aAAa,QAAQ,GAAG,QAAQ,QAAQ;YAC5E,IAAI,QAAQ,UAAU,KAAK,WAAW,aAAa,UAAU,GAAG,QAAQ,UAAU;YAClF,IAAI,QAAQ,OAAO,KAAK,WAAW,aAAa,OAAO,GAAG,QAAQ,OAAO;YACzE,IAAI,QAAQ,QAAQ,KAAK,WAAW,aAAa,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW;YACxF,IAAI,QAAQ,iBAAiB,KAAK,WAAW,aAAa,kBAAkB,GAAG,QAAQ,iBAAiB;YACxG,IAAI,QAAQ,MAAM,KAAK,WAAW,aAAa,MAAM,GAAG,QAAQ,MAAM;YACtE,IAAI,QAAQ,aAAa,KAAK,WAAW,aAAa,cAAc,GAAG,QAAQ,aAAa;YAE5F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM;gBACR;YACF;YAEA,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD,EAAE;YAE7B,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAA8B;QACnD,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM;gBACR;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,MAAkB,EAAgC;QACnE,IAAI;YACF,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,OAAO,MAAM;YAE9B,SAAS;YACT,IAAI,OAAO,MAAM,EAAE;gBACjB,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;oBAChC,QAAQ,MAAM,EAAE,CAAC,UAAU,OAAO,MAAM;gBAC1C,OAAO;oBACL,QAAQ,MAAM,EAAE,CAAC,UAAU,OAAO,MAAM;gBAC1C;YACF;YAEA,SAAS;YACT,IAAI,OAAO,QAAQ,EAAE;gBACnB,IAAI,MAAM,OAAO,CAAC,OAAO,QAAQ,GAAG;oBAClC,QAAQ,MAAM,EAAE,CAAC,YAAY,OAAO,QAAQ;gBAC9C,OAAO;oBACL,QAAQ,MAAM,EAAE,CAAC,YAAY,OAAO,QAAQ;gBAC9C;YACF;YAEA,WAAW;YACX,IAAI,OAAO,SAAS,EAAE;gBACpB,QAAQ,MAAM,GAAG,CAAC,YAAY,OAAO,SAAS,CAAC,WAAW;YAC5D;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,MAAM,GAAG,CAAC,YAAY,OAAO,OAAO,CAAC,WAAW;YAC1D;YAEA,SAAS;YACT,IAAI,OAAO,UAAU,EAAE;gBACrB,QAAQ,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC,qBAAqB,EAAE,OAAO,UAAU,CAAC,CAAC,CAAC;YAChG;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE3E,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;oBACjC,MAAM,EAAE;gBACV;YACF;YAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,sJAAA,CAAA,kBAAe;YAEtC,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM,EAAE;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBACJ,MAAc,EACd,UAAiC,EACjC,IAAqB,EACuB;QAC5C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,SACL,MAAM,CAAC,KAAK;gBAAE,OAAO;YAAQ,GAC7B,EAAE,CAAC,WAAW;YAEjB,OAAO;YACP,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,KAAK,MAAM;gBACrD,QAAQ,MAAM,KAAK,CAAC,QAAQ;oBAAE,WAAW,KAAK,SAAS,KAAK;gBAAM;YACpE,OAAO;gBACL,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;YACvD;YAEA,OAAO;YACP,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,WAAW;YAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;YAErC,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,MAAM,OAAO,EAAE;oBACnC,MAAM;gBACR;YACF;YAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,sJAAA,CAAA,kBAAe;YACtC,MAAM,QAAQ,SAAS;YACvB,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;YAErC,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAiB,EAAE,MAAkB,EAA8B;QACzF,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE;YAAO,GAChB,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE;oBACrC,MAAM;gBACR;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,UAAkB,EAAU;QACxD,MAAM,UAAkC;YACtC,UAAU;YACV,YAAY;YACZ,SAAS;YACT,WAAW;YACX,mBAAmB;QACrB;QAEA,OAAO,OAAO,CAAC,WAAW,IAAI;IAChC;AACF", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/task-management/services/TaskValidationService.ts"], "sourcesContent": ["/**\n * 任务验证服务\n * 负责任务数据的验证和业务规则检查\n */\n\nimport {\n  Task,\n  TaskCategory,\n  Priority,\n  TaskStatus,\n  TASK_CATEGORIES,\n  PRIORITY_LEVELS,\n  TASK_STATUSES,\n  isValidTaskCategory,\n  isValidPriority,\n  isValidTaskStatus\n} from '@/shared';\n\nimport {\n  CreateTaskRequest,\n  UpdateTaskRequest,\n  CompleteTaskRequest,\n  PostponeTaskRequest\n} from '../models/Task';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n}\n\nexport class TaskValidationService {\n\n  /**\n   * 验证任务创建请求\n   */\n  validateCreateRequest(request: CreateTaskRequest): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // 验证必填字段\n    if (!request.userId || request.userId.trim().length === 0) {\n      errors.push('用户ID不能为空');\n    }\n\n    if (!request.title || request.title.trim().length === 0) {\n      errors.push('任务标题不能为空');\n    } else if (request.title.length > 100) {\n      errors.push('任务标题不能超过100个字符');\n    }\n\n    // 验证分类\n    if (!request.category || !isValidTaskCategory(request.category)) {\n      errors.push('请选择有效的任务分类');\n    }\n\n    // 验证重要性\n    if (!isValidPriority(request.importance)) {\n      errors.push('重要性必须在1-5之间');\n    }\n\n    // 验证紧急性\n    if (!isValidPriority(request.urgency)) {\n      errors.push('紧急性必须在1-5之间');\n    }\n\n    // 验证截止时间\n    if (!request.deadline) {\n      errors.push('请设置截止时间');\n    } else {\n      const now = new Date();\n      if (request.deadline < now) {\n        errors.push('截止时间不能早于当前时间');\n      }\n      \n      // 警告：截止时间过于遥远\n      const oneYearLater = new Date();\n      oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);\n      if (request.deadline > oneYearLater) {\n        warnings.push('截止时间设置得较远，建议设置更近的时间以保持紧迫感');\n      }\n    }\n\n    // 验证预估时长\n    if (!request.estimatedDuration || request.estimatedDuration <= 0) {\n      errors.push('预估时长必须大于0');\n    } else if (request.estimatedDuration < 15) {\n      warnings.push('预估时长少于15分钟，建议合并到其他任务中');\n    } else if (request.estimatedDuration > 480) {\n      warnings.push('预估时长超过8小时，建议分解为更小的任务');\n    }\n\n    // 验证描述长度\n    if (request.description && request.description.length > 500) {\n      errors.push('任务描述不能超过500个字符');\n    }\n\n    // 业务规则验证\n    this.validateBusinessRules(request, errors, warnings);\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * 验证任务更新请求\n   */\n  validateUpdateRequest(request: UpdateTaskRequest): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // 验证ID\n    if (!request.id || request.id.trim().length === 0) {\n      errors.push('任务ID不能为空');\n    }\n\n    // 验证标题（如果提供）\n    if (request.title !== undefined) {\n      if (request.title.trim().length === 0) {\n        errors.push('任务标题不能为空');\n      } else if (request.title.length > 100) {\n        errors.push('任务标题不能超过100个字符');\n      }\n    }\n\n    // 验证分类（如果提供）\n    if (request.category !== undefined && !isValidTaskCategory(request.category)) {\n      errors.push('请选择有效的任务分类');\n    }\n\n    // 验证重要性（如果提供）\n    if (request.importance !== undefined && !isValidPriority(request.importance)) {\n      errors.push('重要性必须在1-5之间');\n    }\n\n    // 验证紧急性（如果提供）\n    if (request.urgency !== undefined && !isValidPriority(request.urgency)) {\n      errors.push('紧急性必须在1-5之间');\n    }\n\n    // 验证截止时间（如果提供）\n    if (request.deadline !== undefined) {\n      const now = new Date();\n      if (request.deadline < now) {\n        errors.push('截止时间不能早于当前时间');\n      }\n    }\n\n    // 验证预估时长（如果提供）\n    if (request.estimatedDuration !== undefined) {\n      if (request.estimatedDuration <= 0) {\n        errors.push('预估时长必须大于0');\n      } else if (request.estimatedDuration > 480) {\n        warnings.push('预估时长超过8小时，建议分解为更小的任务');\n      }\n    }\n\n    // 验证状态（如果提供）\n    if (request.status !== undefined && !isValidTaskStatus(request.status)) {\n      errors.push('请选择有效的任务状态');\n    }\n\n    // 验证描述长度（如果提供）\n    if (request.description !== undefined && request.description.length > 500) {\n      errors.push('任务描述不能超过500个字符');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * 验证任务完成请求\n   */\n  validateCompleteRequest(request: CompleteTaskRequest, task: Task): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // 验证任务状态\n    if (task.status === 'completed') {\n      errors.push('任务已经完成');\n    }\n\n    // 验证实际时长\n    if (request.actualDuration !== undefined) {\n      if (request.actualDuration <= 0) {\n        errors.push('实际时长必须大于0');\n      } else {\n        // 检查实际时长与预估时长的差异\n        const estimatedDuration = task.estimatedDuration;\n        const ratio = request.actualDuration / estimatedDuration;\n        \n        if (ratio > 2) {\n          warnings.push('实际时长远超预估时长，建议调整未来类似任务的时间预估');\n        } else if (ratio < 0.5) {\n          warnings.push('实际时长远少于预估时长，建议调整未来类似任务的时间预估');\n        }\n      }\n    }\n\n    // 验证满意度评分\n    if (request.satisfactionScore !== undefined) {\n      if (request.satisfactionScore < 1 || request.satisfactionScore > 5) {\n        errors.push('满意度评分必须在1-5之间');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * 验证任务推迟请求\n   */\n  validatePostponeRequest(request: PostponeTaskRequest, task: Task): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // 验证任务状态\n    if (task.status === 'completed') {\n      errors.push('已完成的任务不能推迟');\n    }\n\n    // 验证推迟次数\n    if (task.postponeCount >= 3) {\n      warnings.push('该任务已推迟多次，建议重新评估任务的必要性或分解任务');\n    }\n\n    // 验证新的截止时间\n    if (request.newDeadline) {\n      const now = new Date();\n      if (request.newDeadline <= now) {\n        errors.push('新的截止时间必须晚于当前时间');\n      }\n      \n      if (request.newDeadline <= task.deadline) {\n        errors.push('新的截止时间必须晚于原截止时间');\n      }\n    }\n\n    // 验证推迟原因\n    if (request.reason && request.reason.length > 200) {\n      errors.push('推迟原因不能超过200个字符');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * 验证业务规则\n   */\n  private validateBusinessRules(request: CreateTaskRequest, errors: string[], warnings: string[]): void {\n    // 规则1：娱乐任务不应该设置过高的重要性和紧急性\n    if (request.category === TASK_CATEGORIES.ENTERTAINMENT) {\n      if (request.importance >= 4 && request.urgency >= 4) {\n        warnings.push('娱乐任务通常不需要设置过高的重要性和紧急性');\n      }\n    }\n\n    // 规则2：工作任务在非工作时间的警告\n    if (request.category === TASK_CATEGORIES.WORK) {\n      const deadlineHour = request.deadline.getHours();\n      if (deadlineHour < 9 || deadlineHour > 18) {\n        warnings.push('工作任务的截止时间设置在非工作时间，请确认是否合理');\n      }\n    }\n\n    // 规则3：高重要性但低紧急性的任务应该有合理的时间安排\n    if (request.importance >= 4 && request.urgency <= 2) {\n      const timeToDeadline = request.deadline.getTime() - new Date().getTime();\n      const daysToDeadline = timeToDeadline / (1000 * 60 * 60 * 24);\n      \n      if (daysToDeadline < 1) {\n        warnings.push('重要但不紧急的任务截止时间过近，可能影响执行质量');\n      }\n    }\n\n    // 规则4：短时间任务的合理性检查\n    if (request.estimatedDuration < 30) {\n      const timeToDeadline = request.deadline.getTime() - new Date().getTime();\n      const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);\n      \n      if (hoursToDeadline > 24 && request.urgency >= 4) {\n        warnings.push('短时间任务设置了较远的截止时间但标记为紧急，请检查设置是否合理');\n      }\n    }\n  }\n\n  /**\n   * 验证任务状态转换的合法性\n   */\n  validateStatusTransition(currentStatus: TaskStatus, newStatus: TaskStatus): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // 定义合法的状态转换\n    const validTransitions: Record<TaskStatus, TaskStatus[]> = {\n      'pending': ['in-progress', 'postponed', 'completed'],\n      'in-progress': ['completed', 'postponed', 'pending'],\n      'completed': [], // 已完成的任务不能转换到其他状态\n      'postponed': ['pending', 'in-progress', 'completed']\n    };\n\n    if (!validTransitions[currentStatus].includes(newStatus)) {\n      errors.push(`不能从状态 \"${currentStatus}\" 转换到 \"${newStatus}\"`);\n    }\n\n    // 特殊情况的警告\n    if (currentStatus === 'completed' && newStatus !== 'completed') {\n      warnings.push('重新激活已完成的任务，请确认是否必要');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AAAA;;AA0BO,MAAM;IAEX;;GAEC,GACD,sBAAsB,OAA0B,EAAoB;QAClE,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,SAAS;QACT,IAAI,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,KAAK;YACrC,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;QACP,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,QAAQ,GAAG;YAC/D,OAAO,IAAI,CAAC;QACd;QAEA,QAAQ;QACR,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,UAAU,GAAG;YACxC,OAAO,IAAI,CAAC;QACd;QAEA,QAAQ;QACR,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,GAAG;YACrC,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QACT,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACrB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,MAAM,MAAM,IAAI;YAChB,IAAI,QAAQ,QAAQ,GAAG,KAAK;gBAC1B,OAAO,IAAI,CAAC;YACd;YAEA,cAAc;YACd,MAAM,eAAe,IAAI;YACzB,aAAa,WAAW,CAAC,aAAa,WAAW,KAAK;YACtD,IAAI,QAAQ,QAAQ,GAAG,cAAc;gBACnC,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,SAAS;QACT,IAAI,CAAC,QAAQ,iBAAiB,IAAI,QAAQ,iBAAiB,IAAI,GAAG;YAChE,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,QAAQ,iBAAiB,GAAG,IAAI;YACzC,SAAS,IAAI,CAAC;QAChB,OAAO,IAAI,QAAQ,iBAAiB,GAAG,KAAK;YAC1C,SAAS,IAAI,CAAC;QAChB;QAEA,SAAS;QACT,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK;YAC3D,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QACT,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ;QAE5C,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,sBAAsB,OAA0B,EAAoB;QAClE,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,OAAO;QACP,IAAI,CAAC,QAAQ,EAAE,IAAI,QAAQ,EAAE,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACjD,OAAO,IAAI,CAAC;QACd;QAEA,aAAa;QACb,IAAI,QAAQ,KAAK,KAAK,WAAW;YAC/B,IAAI,QAAQ,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;gBACrC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,KAAK;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;QAEA,aAAa;QACb,IAAI,QAAQ,QAAQ,KAAK,aAAa,CAAC,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,QAAQ,GAAG;YAC5E,OAAO,IAAI,CAAC;QACd;QAEA,cAAc;QACd,IAAI,QAAQ,UAAU,KAAK,aAAa,CAAC,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,UAAU,GAAG;YAC5E,OAAO,IAAI,CAAC;QACd;QAEA,cAAc;QACd,IAAI,QAAQ,OAAO,KAAK,aAAa,CAAC,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,GAAG;YACtE,OAAO,IAAI,CAAC;QACd;QAEA,eAAe;QACf,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,MAAM,MAAM,IAAI;YAChB,IAAI,QAAQ,QAAQ,GAAG,KAAK;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF;QAEA,eAAe;QACf,IAAI,QAAQ,iBAAiB,KAAK,WAAW;YAC3C,IAAI,QAAQ,iBAAiB,IAAI,GAAG;gBAClC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,QAAQ,iBAAiB,GAAG,KAAK;gBAC1C,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,aAAa;QACb,IAAI,QAAQ,MAAM,KAAK,aAAa,CAAC,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM,GAAG;YACtE,OAAO,IAAI,CAAC;QACd;QAEA,eAAe;QACf,IAAI,QAAQ,WAAW,KAAK,aAAa,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK;YACzE,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,wBAAwB,OAA4B,EAAE,IAAU,EAAoB;QAClF,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,SAAS;QACT,IAAI,KAAK,MAAM,KAAK,aAAa;YAC/B,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QACT,IAAI,QAAQ,cAAc,KAAK,WAAW;YACxC,IAAI,QAAQ,cAAc,IAAI,GAAG;gBAC/B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,iBAAiB;gBACjB,MAAM,oBAAoB,KAAK,iBAAiB;gBAChD,MAAM,QAAQ,QAAQ,cAAc,GAAG;gBAEvC,IAAI,QAAQ,GAAG;oBACb,SAAS,IAAI,CAAC;gBAChB,OAAO,IAAI,QAAQ,KAAK;oBACtB,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,UAAU;QACV,IAAI,QAAQ,iBAAiB,KAAK,WAAW;YAC3C,IAAI,QAAQ,iBAAiB,GAAG,KAAK,QAAQ,iBAAiB,GAAG,GAAG;gBAClE,OAAO,IAAI,CAAC;YACd;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,wBAAwB,OAA4B,EAAE,IAAU,EAAoB;QAClF,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,SAAS;QACT,IAAI,KAAK,MAAM,KAAK,aAAa;YAC/B,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QACT,IAAI,KAAK,aAAa,IAAI,GAAG;YAC3B,SAAS,IAAI,CAAC;QAChB;QAEA,WAAW;QACX,IAAI,QAAQ,WAAW,EAAE;YACvB,MAAM,MAAM,IAAI;YAChB,IAAI,QAAQ,WAAW,IAAI,KAAK;gBAC9B,OAAO,IAAI,CAAC;YACd;YAEA,IAAI,QAAQ,WAAW,IAAI,KAAK,QAAQ,EAAE;gBACxC,OAAO,IAAI,CAAC;YACd;QACF;QAEA,SAAS;QACT,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,KAAK;YACjD,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,OAA0B,EAAE,MAAgB,EAAE,QAAkB,EAAQ;QACpG,0BAA0B;QAC1B,IAAI,QAAQ,QAAQ,KAAK,mIAAA,CAAA,kBAAe,CAAC,aAAa,EAAE;YACtD,IAAI,QAAQ,UAAU,IAAI,KAAK,QAAQ,OAAO,IAAI,GAAG;gBACnD,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,oBAAoB;QACpB,IAAI,QAAQ,QAAQ,KAAK,mIAAA,CAAA,kBAAe,CAAC,IAAI,EAAE;YAC7C,MAAM,eAAe,QAAQ,QAAQ,CAAC,QAAQ;YAC9C,IAAI,eAAe,KAAK,eAAe,IAAI;gBACzC,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,6BAA6B;QAC7B,IAAI,QAAQ,UAAU,IAAI,KAAK,QAAQ,OAAO,IAAI,GAAG;YACnD,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;YACtE,MAAM,iBAAiB,iBAAiB,CAAC,OAAO,KAAK,KAAK,EAAE;YAE5D,IAAI,iBAAiB,GAAG;gBACtB,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,kBAAkB;QAClB,IAAI,QAAQ,iBAAiB,GAAG,IAAI;YAClC,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;YACtE,MAAM,kBAAkB,iBAAiB,CAAC,OAAO,KAAK,EAAE;YAExD,IAAI,kBAAkB,MAAM,QAAQ,OAAO,IAAI,GAAG;gBAChD,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA;;GAEC,GACD,yBAAyB,aAAyB,EAAE,SAAqB,EAAoB;QAC3F,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,YAAY;QACZ,MAAM,mBAAqD;YACzD,WAAW;gBAAC;gBAAe;gBAAa;aAAY;YACpD,eAAe;gBAAC;gBAAa;gBAAa;aAAU;YACpD,aAAa,EAAE;YACf,aAAa;gBAAC;gBAAW;gBAAe;aAAY;QACtD;QAEA,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY;YACxD,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,OAAO,EAAE,UAAU,CAAC,CAAC;QAC3D;QAEA,UAAU;QACV,IAAI,kBAAkB,eAAe,cAAc,aAAa;YAC9D,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/task-management/services/TaskService.ts"], "sourcesContent": ["/**\n * 任务服务\n * 提供任务管理的核心业务逻辑\n */\n\nimport {\n  Task,\n  TaskCategory,\n  TaskStatus,\n  ApiResponse,\n  createAppError,\n  ERROR_CODES,\n  classifyQuadrant,\n  sortTasksByPriority,\n  sortTasksByDeadline,\n  filterTasksByCategory,\n  filterTasksByStatus,\n  isTaskOverdue,\n  isTaskDueSoon\n} from '@/shared';\n\nimport {\n  CreateTaskRequest,\n  UpdateTaskRequest,\n  CompleteTaskRequest,\n  PostponeTaskRequest,\n  TaskFilter,\n  TaskSortConfig,\n  TaskPaginationOptions,\n  TaskPaginationResult,\n  TaskStatistics\n} from '../models/Task';\n\nimport { TaskRepository } from '../repositories/TaskRepository';\nimport { TaskValidationService } from './TaskValidationService';\n\nexport class TaskService {\n  private repository: TaskRepository;\n  private validationService: TaskValidationService;\n\n  constructor() {\n    this.repository = new TaskRepository();\n    this.validationService = new TaskValidationService();\n  }\n\n  // ============================================================================\n  // 基础 CRUD 操作\n  // ============================================================================\n\n  /**\n   * 获取用户的所有任务\n   */\n  async getUserTasks(userId: string): Promise<ApiResponse<Task[]>> {\n    if (!userId) {\n      return {\n        success: false,\n        error: '用户ID不能为空',\n        data: []\n      };\n    }\n\n    return await this.repository.findByUserId(userId);\n  }\n\n  /**\n   * 根据ID获取任务\n   */\n  async getTaskById(id: string): Promise<ApiResponse<Task | null>> {\n    if (!id) {\n      return {\n        success: false,\n        error: '任务ID不能为空',\n        data: null\n      };\n    }\n\n    return await this.repository.findById(id);\n  }\n\n  /**\n   * 创建新任务\n   */\n  async createTask(request: CreateTaskRequest): Promise<ApiResponse<Task>> {\n    // 验证请求数据\n    const validation = this.validationService.validateCreateRequest(request);\n    if (!validation.isValid) {\n      return {\n        success: false,\n        error: validation.errors.join('; '),\n        data: null as any\n      };\n    }\n\n    // 创建任务\n    const result = await this.repository.create(request);\n    \n    if (result.success && validation.warnings.length > 0) {\n      console.warn('任务创建警告:', validation.warnings);\n    }\n\n    return result;\n  }\n\n  /**\n   * 更新任务\n   */\n  async updateTask(request: UpdateTaskRequest): Promise<ApiResponse<Task>> {\n    // 验证请求数据\n    const validation = this.validationService.validateUpdateRequest(request);\n    if (!validation.isValid) {\n      return {\n        success: false,\n        error: validation.errors.join('; '),\n        data: null as any\n      };\n    }\n\n    // 如果更新状态，验证状态转换\n    if (request.status) {\n      const currentTaskResult = await this.repository.findById(request.id);\n      if (!currentTaskResult.success || !currentTaskResult.data) {\n        return {\n          success: false,\n          error: '任务不存在',\n          data: null as any\n        };\n      }\n\n      const statusValidation = this.validationService.validateStatusTransition(\n        currentTaskResult.data.status,\n        request.status\n      );\n      \n      if (!statusValidation.isValid) {\n        return {\n          success: false,\n          error: statusValidation.errors.join('; '),\n          data: null as any\n        };\n      }\n    }\n\n    return await this.repository.update(request);\n  }\n\n  /**\n   * 删除任务\n   */\n  async deleteTask(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      return {\n        success: false,\n        error: '任务ID不能为空',\n        data: undefined\n      };\n    }\n\n    // 检查任务是否存在\n    const taskResult = await this.repository.findById(id);\n    if (!taskResult.success || !taskResult.data) {\n      return {\n        success: false,\n        error: '任务不存在',\n        data: undefined\n      };\n    }\n\n    return await this.repository.delete(id);\n  }\n\n  // ============================================================================\n  // 任务状态管理\n  // ============================================================================\n\n  /**\n   * 完成任务\n   */\n  async completeTask(request: CompleteTaskRequest): Promise<ApiResponse<Task>> {\n    // 获取当前任务\n    const taskResult = await this.repository.findById(request.id);\n    if (!taskResult.success || !taskResult.data) {\n      return {\n        success: false,\n        error: '任务不存在',\n        data: null as any\n      };\n    }\n\n    const task = taskResult.data;\n\n    // 验证完成请求\n    const validation = this.validationService.validateCompleteRequest(request, task);\n    if (!validation.isValid) {\n      return {\n        success: false,\n        error: validation.errors.join('; '),\n        data: null as any\n      };\n    }\n\n    // 更新任务状态\n    const updateResult = await this.repository.update({\n      id: request.id,\n      status: 'completed'\n    });\n\n    if (updateResult.success && validation.warnings.length > 0) {\n      console.warn('任务完成警告:', validation.warnings);\n    }\n\n    return updateResult;\n  }\n\n  /**\n   * 推迟任务\n   */\n  async postponeTask(request: PostponeTaskRequest): Promise<ApiResponse<Task>> {\n    // 获取当前任务\n    const taskResult = await this.repository.findById(request.id);\n    if (!taskResult.success || !taskResult.data) {\n      return {\n        success: false,\n        error: '任务不存在',\n        data: null as any\n      };\n    }\n\n    const task = taskResult.data;\n\n    // 验证推迟请求\n    const validation = this.validationService.validatePostponeRequest(request, task);\n    if (!validation.isValid) {\n      return {\n        success: false,\n        error: validation.errors.join('; '),\n        data: null as any\n      };\n    }\n\n    // 更新任务\n    const updateData: UpdateTaskRequest = {\n      id: request.id,\n      status: 'postponed',\n      postponeCount: task.postponeCount + 1\n    };\n\n    if (request.newDeadline) {\n      updateData.deadline = request.newDeadline;\n    }\n\n    const updateResult = await this.repository.update(updateData);\n\n    if (updateResult.success && validation.warnings.length > 0) {\n      console.warn('任务推迟警告:', validation.warnings);\n    }\n\n    return updateResult;\n  }\n\n  /**\n   * 开始任务\n   */\n  async startTask(id: string): Promise<ApiResponse<Task>> {\n    return await this.updateTask({\n      id,\n      status: 'in-progress'\n    });\n  }\n\n  /**\n   * 暂停任务\n   */\n  async pauseTask(id: string): Promise<ApiResponse<Task>> {\n    return await this.updateTask({\n      id,\n      status: 'pending'\n    });\n  }\n\n  // ============================================================================\n  // 任务查询和过滤\n  // ============================================================================\n\n  /**\n   * 根据条件过滤任务\n   */\n  async getTasksByFilter(filter: TaskFilter): Promise<ApiResponse<Task[]>> {\n    return await this.repository.findByFilter(filter);\n  }\n\n  /**\n   * 分页获取任务\n   */\n  async getTasksWithPagination(\n    userId: string,\n    pagination: TaskPaginationOptions,\n    sort?: TaskSortConfig\n  ): Promise<ApiResponse<TaskPaginationResult>> {\n    if (!userId) {\n      return {\n        success: false,\n        error: '用户ID不能为空',\n        data: null as any\n      };\n    }\n\n    return await this.repository.findWithPagination(userId, pagination, sort);\n  }\n\n  /**\n   * 获取今日任务\n   */\n  async getTodayTasks(userId: string): Promise<ApiResponse<Task[]>> {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    const filter: TaskFilter = {\n      userId,\n      status: ['pending', 'in-progress'],\n      endDate: tomorrow\n    };\n\n    return await this.getTasksByFilter(filter);\n  }\n\n  /**\n   * 获取过期任务\n   */\n  async getOverdueTasks(userId: string): Promise<ApiResponse<Task[]>> {\n    const tasksResult = await this.getUserTasks(userId);\n    if (!tasksResult.success) {\n      return tasksResult;\n    }\n\n    const overdueTasks = tasksResult.data.filter(task => \n      isTaskOverdue(task) && task.status !== 'completed'\n    );\n\n    return {\n      success: true,\n      data: overdueTasks\n    };\n  }\n\n  /**\n   * 获取即将到期的任务\n   */\n  async getDueSoonTasks(userId: string, hoursThreshold: number = 24): Promise<ApiResponse<Task[]>> {\n    const tasksResult = await this.getUserTasks(userId);\n    if (!tasksResult.success) {\n      return tasksResult;\n    }\n\n    const dueSoonTasks = tasksResult.data.filter(task => \n      isTaskDueSoon(task, hoursThreshold) && task.status !== 'completed'\n    );\n\n    return {\n      success: true,\n      data: dueSoonTasks\n    };\n  }\n\n  // ============================================================================\n  // 任务统计\n  // ============================================================================\n\n  /**\n   * 获取任务统计信息\n   */\n  async getTaskStatistics(userId: string): Promise<ApiResponse<TaskStatistics>> {\n    const tasksResult = await this.getUserTasks(userId);\n    if (!tasksResult.success) {\n      return {\n        success: false,\n        error: tasksResult.error,\n        data: null as any\n      };\n    }\n\n    const tasks = tasksResult.data;\n    \n    // 按状态统计\n    const byStatus = tasks.reduce((acc, task) => {\n      acc[task.status] = (acc[task.status] || 0) + 1;\n      return acc;\n    }, {} as Record<TaskStatus, number>);\n\n    // 按分类统计\n    const byCategory = tasks.reduce((acc, task) => {\n      acc[task.category] = (acc[task.category] || 0) + 1;\n      return acc;\n    }, {} as Record<TaskCategory, number>);\n\n    // 按象限统计\n    const byQuadrant = tasks.reduce((acc, task) => {\n      const quadrant = classifyQuadrant(task.importance, task.urgency);\n      acc[quadrant] = (acc[quadrant] || 0) + 1;\n      return acc;\n    }, {} as Record<number, number>);\n\n    // 过期和即将到期任务数量\n    const overdue = tasks.filter(task => isTaskOverdue(task) && task.status !== 'completed').length;\n    const dueSoon = tasks.filter(task => isTaskDueSoon(task) && task.status !== 'completed').length;\n\n    // 平均完成时间（这里简化处理，实际应该从完成记录中计算）\n    const completedTasks = tasks.filter(task => task.status === 'completed');\n    const averageCompletionTime = completedTasks.length > 0 \n      ? completedTasks.reduce((sum, task) => sum + task.estimatedDuration, 0) / completedTasks.length\n      : 0;\n\n    const statistics: TaskStatistics = {\n      total: tasks.length,\n      byStatus: byStatus as Record<TaskStatus, number>,\n      byCategory: byCategory as Record<TaskCategory, number>,\n      byQuadrant: byQuadrant as Record<any, number>,\n      overdue,\n      dueSoon,\n      averageCompletionTime\n    };\n\n    return {\n      success: true,\n      data: statistics\n    };\n  }\n\n  // ============================================================================\n  // 批量操作\n  // ============================================================================\n\n  /**\n   * 批量更新任务状态\n   */\n  async batchUpdateStatus(taskIds: string[], status: TaskStatus): Promise<ApiResponse<void>> {\n    if (!taskIds || taskIds.length === 0) {\n      return {\n        success: false,\n        error: '任务ID列表不能为空',\n        data: undefined\n      };\n    }\n\n    return await this.repository.batchUpdateStatus(taskIds, status);\n  }\n\n  /**\n   * 批量删除任务\n   */\n  async batchDeleteTasks(taskIds: string[]): Promise<ApiResponse<void>> {\n    if (!taskIds || taskIds.length === 0) {\n      return {\n        success: false,\n        error: '任务ID列表不能为空',\n        data: undefined\n      };\n    }\n\n    try {\n      for (const id of taskIds) {\n        const result = await this.repository.delete(id);\n        if (!result.success) {\n          return {\n            success: false,\n            error: `删除任务 ${id} 失败: ${result.error}`,\n            data: undefined\n          };\n        }\n      }\n\n      return {\n        success: true,\n        data: undefined\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '批量删除任务时发生未知错误',\n        data: undefined\n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AA4BA;AACA;;;;AAEO,MAAM;IACH,WAA2B;IAC3B,kBAAyC;IAEjD,aAAc;QACZ,IAAI,CAAC,UAAU,GAAG,IAAI,sKAAA,CAAA,iBAAc;QACpC,IAAI,CAAC,iBAAiB,GAAG,IAAI,yKAAA,CAAA,wBAAqB;IACpD;IAEA,+EAA+E;IAC/E,aAAa;IACb,+EAA+E;IAE/E;;GAEC,GACD,MAAM,aAAa,MAAc,EAAgC;QAC/D,IAAI,CAAC,QAAQ;YACX,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM,EAAE;YACV;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC5C;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAqC;QAC/D,IAAI,CAAC,IAAI;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACxC;IAEA;;GAEC,GACD,MAAM,WAAW,OAA0B,EAA8B;QACvE,SAAS;QACT,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;QAChE,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC;gBAC9B,MAAM;YACR;QACF;QAEA,OAAO;QACP,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAE5C,IAAI,OAAO,OAAO,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;YACpD,QAAQ,IAAI,CAAC,WAAW,WAAW,QAAQ;QAC7C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,WAAW,OAA0B,EAA8B;QACvE,SAAS;QACT,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;QAChE,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC;gBAC9B,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,oBAAoB,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACnE,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;gBACzD,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,MAAM,mBAAmB,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CACtE,kBAAkB,IAAI,CAAC,MAAM,EAC7B,QAAQ,MAAM;YAGhB,IAAI,CAAC,iBAAiB,OAAO,EAAE;gBAC7B,OAAO;oBACL,SAAS;oBACT,OAAO,iBAAiB,MAAM,CAAC,IAAI,CAAC;oBACpC,MAAM;gBACR;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IACtC;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAA8B;QACvD,IAAI,CAAC,IAAI;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,WAAW;QACX,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClD,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IACtC;IAEA,+EAA+E;IAC/E,SAAS;IACT,+EAA+E;IAE/E;;GAEC,GACD,MAAM,aAAa,OAA4B,EAA8B;QAC3E,SAAS;QACT,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE;QAC5D,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,MAAM,OAAO,WAAW,IAAI;QAE5B,SAAS;QACT,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,SAAS;QAC3E,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC;gBAC9B,MAAM;YACR;QACF;QAEA,SAAS;QACT,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YAChD,IAAI,QAAQ,EAAE;YACd,QAAQ;QACV;QAEA,IAAI,aAAa,OAAO,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC1D,QAAQ,IAAI,CAAC,WAAW,WAAW,QAAQ;QAC7C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,aAAa,OAA4B,EAA8B;QAC3E,SAAS;QACT,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE;QAC5D,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,MAAM,OAAO,WAAW,IAAI;QAE5B,SAAS;QACT,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,SAAS;QAC3E,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC;gBAC9B,MAAM;YACR;QACF;QAEA,OAAO;QACP,MAAM,aAAgC;YACpC,IAAI,QAAQ,EAAE;YACd,QAAQ;YACR,eAAe,KAAK,aAAa,GAAG;QACtC;QAEA,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,QAAQ,GAAG,QAAQ,WAAW;QAC3C;QAEA,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAElD,IAAI,aAAa,OAAO,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC1D,QAAQ,IAAI,CAAC,WAAW,WAAW,QAAQ;QAC7C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAA8B;QACtD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC;YAC3B;YACA,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAA8B;QACtD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC;YAC3B;YACA,QAAQ;QACV;IACF;IAEA,+EAA+E;IAC/E,UAAU;IACV,+EAA+E;IAE/E;;GAEC,GACD,MAAM,iBAAiB,MAAkB,EAAgC;QACvE,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC5C;IAEA;;GAEC,GACD,MAAM,uBACJ,MAAc,EACd,UAAiC,EACjC,IAAqB,EACuB;QAC5C,IAAI,CAAC,QAAQ;YACX,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,QAAQ,YAAY;IACtE;IAEA;;GAEC,GACD,MAAM,cAAc,MAAc,EAAgC;QAChE,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,MAAM,SAAqB;YACzB;YACA,QAAQ;gBAAC;gBAAW;aAAc;YAClC,SAAS;QACX;QAEA,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC;IACrC;IAEA;;GAEC,GACD,MAAM,gBAAgB,MAAc,EAAgC;QAClE,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY,CAAC;QAC5C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,OAAO;QACT;QAEA,MAAM,eAAe,YAAY,IAAI,CAAC,MAAM,CAAC,CAAA,OAC3C,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,MAAM,KAAK;QAGzC,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,MAAc,EAAE,iBAAyB,EAAE,EAAgC;QAC/F,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY,CAAC;QAC5C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,OAAO;QACT;QAEA,MAAM,eAAe,YAAY,IAAI,CAAC,MAAM,CAAC,CAAA,OAC3C,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,mBAAmB,KAAK,MAAM,KAAK;QAGzD,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF;IAEA,+EAA+E;IAC/E,OAAO;IACP,+EAA+E;IAE/E;;GAEC,GACD,MAAM,kBAAkB,MAAc,EAAwC;QAC5E,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY,CAAC;QAC5C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,OAAO;gBACL,SAAS;gBACT,OAAO,YAAY,KAAK;gBACxB,MAAM;YACR;QACF;QAEA,MAAM,QAAQ,YAAY,IAAI;QAE9B,QAAQ;QACR,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK;YAClC,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QAEJ,QAAQ;QACR,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK;YACpC,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QAEJ,QAAQ;QACR,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK;YACpC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI;YACvC,OAAO;QACT,GAAG,CAAC;QAEJ,cAAc;QACd,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,MAAM,KAAK,aAAa,MAAM;QAC/F,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,MAAM,KAAK,aAAa,MAAM;QAE/F,8BAA8B;QAC9B,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QAC5D,MAAM,wBAAwB,eAAe,MAAM,GAAG,IAClD,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,iBAAiB,EAAE,KAAK,eAAe,MAAM,GAC7F;QAEJ,MAAM,aAA6B;YACjC,OAAO,MAAM,MAAM;YACnB,UAAU;YACV,YAAY;YACZ,YAAY;YACZ;YACA;YACA;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF;IAEA,+EAA+E;IAC/E,OAAO;IACP,+EAA+E;IAE/E;;GAEC,GACD,MAAM,kBAAkB,OAAiB,EAAE,MAAkB,EAA8B;QACzF,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS;IAC1D;IAEA;;GAEC,GACD,MAAM,iBAAiB,OAAiB,EAA8B;QACpE,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,MAAM;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAM,MAAM,QAAS;gBACxB,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBAC5C,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,OAAO;wBACL,SAAS;wBACT,OAAO,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,OAAO,KAAK,EAAE;wBACvC,MAAM;oBACR;gBACF;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3522, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/domains/task-management/index.ts"], "sourcesContent": ["/**\n * 任务管理域统一导出\n * 提供任务管理相关的所有功能\n */\n\n// ============================================================================\n// 模型导出\n// ============================================================================\n\nexport type {\n  TaskRecord,\n  CreateTaskRequest,\n  UpdateTaskRequest,\n  CompleteTaskRequest,\n  PostponeTaskRequest,\n  TaskFilter,\n  TaskSortOption,\n  SortDirection,\n  TaskSortConfig,\n  TaskPaginationOptions,\n  TaskPaginationResult,\n  TaskStatistics,\n  TaskCompletion\n} from './models/Task';\n\nexport {\n  mapRecordToTask,\n  mapTaskToRecord\n} from './models/Task';\n\n// ============================================================================\n// 仓储导出\n// ============================================================================\n\nexport { TaskRepository } from './repositories/TaskRepository';\n\n// ============================================================================\n// 服务导出\n// ============================================================================\n\nexport { TaskService } from './services/TaskService';\nexport { TaskValidationService } from './services/TaskValidationService';\nexport type { ValidationResult } from './services/TaskValidationService';\n\n// ============================================================================\n// 便捷实例创建\n// ============================================================================\n\nimport { TaskService } from './services/TaskService';\nimport { TaskRepository } from './repositories/TaskRepository';\nimport { TaskValidationService } from './services/TaskValidationService';\n\n/**\n * 创建任务服务实例\n */\nexport function createTaskService(): TaskService {\n  return new TaskService();\n}\n\n/**\n * 创建任务仓储实例\n */\nexport function createTaskRepository(): TaskRepository {\n  return new TaskRepository();\n}\n\n/**\n * 创建任务验证服务实例\n */\nexport function createTaskValidationService(): TaskValidationService {\n  return new TaskValidationService();\n}\n\n// ============================================================================\n// 默认实例（单例模式）\n// ============================================================================\n\nlet defaultTaskService: TaskService | null = null;\n\n/**\n * 获取默认的任务服务实例（单例）\n */\nexport function getDefaultTaskService(): TaskService {\n  if (!defaultTaskService) {\n    defaultTaskService = new TaskService();\n  }\n  return defaultTaskService;\n}\n\n/**\n * 重置默认实例\n */\nexport function resetDefaultTaskService(): void {\n  defaultTaskService = null;\n}\n\n// ============================================================================\n// 类型重新导出（从 shared 模块）\n// ============================================================================\n\nexport type {\n  Task,\n  TaskCategory,\n  Priority,\n  TaskStatus,\n  Quadrant\n} from '@/shared';\n\n// ============================================================================\n// 工具函数\n// ============================================================================\n\n/**\n * 验证任务服务的健康状态\n */\nexport async function validateTaskServiceHealth(): Promise<{\n  isHealthy: boolean;\n  errors: string[];\n  timestamp: Date;\n}> {\n  const errors: string[] = [];\n  let isHealthy = true;\n\n  try {\n    const service = getDefaultTaskService();\n    \n    // 尝试创建一个测试验证\n    const testRequest = {\n      userId: 'test',\n      title: 'Test Task',\n      category: 'work' as TaskCategory,\n      importance: 3 as Priority,\n      urgency: 3 as Priority,\n      deadline: new Date(),\n      estimatedDuration: 60\n    };\n    \n    const validation = service['validationService'].validateCreateRequest(testRequest);\n    if (!validation) {\n      errors.push('任务验证服务不可用');\n      isHealthy = false;\n    }\n  } catch (error) {\n    errors.push(`任务服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    isHealthy = false;\n  }\n\n  return {\n    isHealthy,\n    errors,\n    timestamp: new Date()\n  };\n}\n\n/**\n * 获取任务管理域的功能清单\n */\nexport function getTaskManagementDomainFeatures(): {\n  models: string[];\n  repositories: string[];\n  services: string[];\n  capabilities: string[];\n} {\n  return {\n    models: [\n      'Task - 核心任务模型',\n      'TaskRecord - 数据库记录映射',\n      'CreateTaskRequest - 任务创建请求',\n      'UpdateTaskRequest - 任务更新请求',\n      'CompleteTaskRequest - 任务完成请求',\n      'PostponeTaskRequest - 任务推迟请求',\n      'TaskFilter - 任务过滤条件',\n      'TaskStatistics - 任务统计信息'\n    ],\n    repositories: [\n      'TaskRepository - 任务数据持久化'\n    ],\n    services: [\n      'TaskService - 核心任务业务逻辑',\n      'TaskValidationService - 任务数据验证'\n    ],\n    capabilities: [\n      '完整的任务 CRUD 操作',\n      '任务状态生命周期管理',\n      '任务数据验证和业务规则检查',\n      '任务过滤、排序和分页',\n      '任务统计和分析',\n      '批量任务操作',\n      '任务完成和推迟处理',\n      '过期和即将到期任务检测',\n      '任务状态转换验证',\n      '业务规则和警告提示'\n    ]\n  };\n}\n\n/**\n * 获取任务管理的性能基准测试\n */\nexport async function runTaskManagementBenchmark(): Promise<{\n  createTime: number;\n  readTime: number;\n  updateTime: number;\n  deleteTime: number;\n  validationTime: number;\n  totalTime: number;\n}> {\n  const service = createTaskService();\n  const validationService = createTaskValidationService();\n  \n  // 模拟测试数据\n  const testRequest: CreateTaskRequest = {\n    userId: 'benchmark-test',\n    title: '性能测试任务',\n    category: 'work',\n    importance: 3,\n    urgency: 3,\n    deadline: new Date(),\n    estimatedDuration: 60\n  };\n\n  const startTime = Date.now();\n  \n  // 测试验证性能\n  const validationStart = Date.now();\n  validationService.validateCreateRequest(testRequest);\n  const validationTime = Date.now() - validationStart;\n\n  // 注意：这里只测试验证逻辑，不执行实际的数据库操作\n  // 实际的 CRUD 操作需要数据库连接\n  \n  const createTime = 0; // 模拟值\n  const readTime = 0;   // 模拟值\n  const updateTime = 0; // 模拟值\n  const deleteTime = 0; // 模拟值\n\n  const totalTime = Date.now() - startTime;\n\n  return {\n    createTime,\n    readTime,\n    updateTime,\n    deleteTime,\n    validationTime,\n    totalTime\n  };\n}\n\n/**\n * 获取任务管理域的配置选项\n */\nexport function getTaskManagementConfig(): {\n  maxTitleLength: number;\n  maxDescriptionLength: number;\n  maxPostponeCount: number;\n  defaultPageSize: number;\n  maxPageSize: number;\n} {\n  return {\n    maxTitleLength: 100,\n    maxDescriptionLength: 500,\n    maxPostponeCount: 5,\n    defaultPageSize: 20,\n    maxPageSize: 100\n  };\n}\n\n/**\n * 创建任务管理域的示例数据\n */\nexport function createSampleTaskData(): CreateTaskRequest[] {\n  return [\n    {\n      userId: 'sample-user',\n      title: '完成项目报告',\n      description: '撰写季度项目总结报告',\n      category: 'work',\n      importance: 4,\n      urgency: 3,\n      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后\n      estimatedDuration: 120\n    },\n    {\n      userId: 'sample-user',\n      title: '学习新技术',\n      description: '学习 React 18 的新特性',\n      category: 'improvement',\n      importance: 3,\n      urgency: 2,\n      deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后\n      estimatedDuration: 180\n    },\n    {\n      userId: 'sample-user',\n      title: '看电影放松',\n      description: '观看最新上映的电影',\n      category: 'entertainment',\n      importance: 2,\n      urgency: 1,\n      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后\n      estimatedDuration: 120\n    }\n  ];\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;;;;;;;;;;;;;AAkB/E;AAKA,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E;AAEA,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E;AACA;;;;;;;;AAcO,SAAS;IACd,OAAO,IAAI,+JAAA,CAAA,cAAW;AACxB;AAKO,SAAS;IACd,OAAO,IAAI,sKAAA,CAAA,iBAAc;AAC3B;AAKO,SAAS;IACd,OAAO,IAAI,yKAAA,CAAA,wBAAqB;AAClC;AAEA,+EAA+E;AAC/E,aAAa;AACb,+EAA+E;AAE/E,IAAI,qBAAyC;AAKtC,SAAS;IACd,IAAI,CAAC,oBAAoB;QACvB,qBAAqB,IAAI,+JAAA,CAAA,cAAW;IACtC;IACA,OAAO;AACT;AAKO,SAAS;IACd,qBAAqB;AACvB;AAqBO,eAAe;IAKpB,MAAM,SAAmB,EAAE;IAC3B,IAAI,YAAY;IAEhB,IAAI;QACF,MAAM,UAAU;QAEhB,aAAa;QACb,MAAM,cAAc;YAClB,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU,IAAI;YACd,mBAAmB;QACrB;QAEA,MAAM,aAAa,OAAO,CAAC,oBAAoB,CAAC,qBAAqB,CAAC;QACtE,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;YACZ,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC3E,YAAY;IACd;IAEA,OAAO;QACL;QACA;QACA,WAAW,IAAI;IACjB;AACF;AAKO,SAAS;IAMd,OAAO;QACL,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;SACD;QACD,UAAU;YACR;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,eAAe;IAQpB,MAAM,UAAU;IAChB,MAAM,oBAAoB;IAE1B,SAAS;IACT,MAAM,cAAiC;QACrC,QAAQ;QACR,OAAO;QACP,UAAU;QACV,YAAY;QACZ,SAAS;QACT,UAAU,IAAI;QACd,mBAAmB;IACrB;IAEA,MAAM,YAAY,KAAK,GAAG;IAE1B,SAAS;IACT,MAAM,kBAAkB,KAAK,GAAG;IAChC,kBAAkB,qBAAqB,CAAC;IACxC,MAAM,iBAAiB,KAAK,GAAG,KAAK;IAEpC,2BAA2B;IAC3B,qBAAqB;IAErB,MAAM,aAAa,GAAG,MAAM;IAC5B,MAAM,WAAW,GAAK,MAAM;IAC5B,MAAM,aAAa,GAAG,MAAM;IAC5B,MAAM,aAAa,GAAG,MAAM;IAE5B,MAAM,YAAY,KAAK,GAAG,KAAK;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IAOd,OAAO;QACL,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,aAAa;IACf;AACF;AAKO,SAAS;IACd,OAAO;QACL;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACnD,mBAAmB;QACrB;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YACpD,mBAAmB;QACrB;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACnD,mBAAmB;QACrB;KACD;AACH", "debugId": null}}, {"offset": {"line": 3737, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/store/useTaskStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/shared';\r\nimport { supabase } from '@/lib/supabase';\r\nimport { getDefaultPlanningService } from '@/domains/intelligent-planning';\r\nimport { getDefaultTaskService, CreateTaskRequest, UpdateTaskRequest } from '@/domains/task-management';\r\n\r\ninterface TaskState {\r\n  // State\r\n  tasks: Task[];\r\n  dailySchedule: DailySchedule | null;\r\n  balanceAnalysis: BalanceAnalysis | null;\r\n  postponedAlerts: PostponedTaskAlert[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  \r\n  // Services\r\n  planningService: ReturnType<typeof getDefaultPlanningService>;\r\n  taskService: ReturnType<typeof getDefaultTaskService>;\r\n  \r\n  // Actions\r\n  fetchTasks: (userId: string) => Promise<void>;\r\n  createTask: (task: CreateTaskRequest) => Promise<void>;\r\n  updateTask: (id: string, updates: Partial<UpdateTaskRequest>) => Promise<void>;\r\n  deleteTask: (id: string) => Promise<void>;\r\n  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;\r\n  postponeTask: (id: string, reason?: string) => Promise<void>;\r\n  startTask: (id: string) => Promise<void>;\r\n  pauseTask: (id: string) => Promise<void>;\r\n  \r\n  // Algorithm actions\r\n  generateDailySchedule: (userId: string) => Promise<void>;\r\n  analyzeBalance: (userId: string) => Promise<void>;\r\n  analyzePostponedTasks: (userId: string) => Promise<void>;\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useTaskStore = create<TaskState>((set, get) => ({\r\n  // Initial state\r\n  tasks: [],\r\n  dailySchedule: null,\r\n  balanceAnalysis: null,\r\n  postponedAlerts: [],\r\n  loading: false,\r\n  error: null,\r\n  \r\n  // Service instances\r\n  planningService: getDefaultPlanningService(),\r\n  taskService: getDefaultTaskService(),\r\n  \r\n  // Fetch tasks\r\n  fetchTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { taskService } = get();\r\n      const result = await taskService.getUserTasks(userId);\r\n\r\n      if (result.success) {\r\n        set({ tasks: result.data, loading: false });\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to fetch tasks',\r\n          loading: false\r\n        });\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Create task\r\n  createTask: async (taskData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { taskService } = get();\r\n      const result = await taskService.createTask(taskData);\r\n\r\n      if (result.success) {\r\n        set(state => ({\r\n          tasks: [result.data, ...state.tasks],\r\n          loading: false\r\n        }));\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to create task',\r\n          loading: false\r\n        });\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Update task\r\n  updateTask: async (id: string, updates: Partial<UpdateTaskRequest>) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { taskService } = get();\r\n      const result = await taskService.updateTask({ id, ...updates });\r\n\r\n      if (result.success) {\r\n        set(state => ({\r\n          tasks: state.tasks.map(task =>\r\n            task.id === id ? result.data : task\r\n          ),\r\n          loading: false\r\n        }));\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to update task',\r\n          loading: false\r\n        });\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Delete task\r\n  deleteTask: async (id: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { taskService } = get();\r\n      const result = await taskService.deleteTask(id);\r\n\r\n      if (result.success) {\r\n        set(state => ({\r\n          tasks: state.tasks.filter(task => task.id !== id),\r\n          loading: false\r\n        }));\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to delete task',\r\n          loading: false\r\n        });\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Complete task\r\n  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {\r\n    try {\r\n      const { taskService, planningService } = get();\r\n      const task = get().tasks.find(t => t.id === id);\r\n\r\n      if (!task) throw new Error('Task not found');\r\n\r\n      // Complete task using task service\r\n      const result = await taskService.completeTask({\r\n        id,\r\n        actualDuration,\r\n        satisfactionScore: satisfaction\r\n      });\r\n\r\n      if (result.success) {\r\n        // Update local state\r\n        set(state => ({\r\n          tasks: state.tasks.map(t =>\r\n            t.id === id ? result.data : t\r\n          )\r\n        }));\r\n\r\n        // Update daily stats\r\n        await planningService.updateActivityStats(\r\n          task.userId,\r\n          task.category,\r\n          actualDuration || task.estimatedDuration\r\n        );\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n      \r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Postpone task\r\n  postponeTask: async (id: string, reason?: string) => {\r\n    try {\r\n      const { taskService } = get();\r\n      const result = await taskService.postponeTask({\r\n        id,\r\n        reason\r\n      });\r\n\r\n      if (result.success) {\r\n        // Update local state\r\n        set(state => ({\r\n          tasks: state.tasks.map(t =>\r\n            t.id === id ? result.data : t\r\n          )\r\n        }));\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Start task\r\n  startTask: async (id: string) => {\r\n    try {\r\n      const { taskService } = get();\r\n      const result = await taskService.startTask(id);\r\n\r\n      if (result.success) {\r\n        // Update local state\r\n        set(state => ({\r\n          tasks: state.tasks.map(t =>\r\n            t.id === id ? result.data : t\r\n          )\r\n        }));\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to start task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Pause task\r\n  pauseTask: async (id: string) => {\r\n    try {\r\n      const { taskService } = get();\r\n      const result = await taskService.pauseTask(id);\r\n\r\n      if (result.success) {\r\n        // Update local state\r\n        set(state => ({\r\n          tasks: state.tasks.map(t =>\r\n            t.id === id ? result.data : t\r\n          )\r\n        }));\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to pause task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Generate daily schedule\r\n  generateDailySchedule: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { tasks, planningService } = get();\r\n      const userTasks = tasks.filter(task => task.userId === userId);\r\n\r\n      // 获取用户时间配置\r\n      let userTimeConfig = null;\r\n      try {\r\n        const { data: userProfile } = await supabase\r\n          .from('user_profiles')\r\n          .select('time_config')\r\n          .eq('id', userId)\r\n          .single();\r\n\r\n        userTimeConfig = userProfile?.time_config;\r\n      } catch (error) {\r\n        console.log('No user time config found, using defaults');\r\n      }\r\n\r\n      // 使用新的规划服务\r\n      const result = await planningService.generateDailyPlan(userId, userTasks, userTimeConfig);\r\n\r\n      if (result.success) {\r\n        set({\r\n          dailySchedule: result.data.schedule,\r\n          loading: false\r\n        });\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to generate schedule',\r\n          loading: false\r\n        });\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze balance\r\n  analyzeBalance: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { planningService } = get();\r\n      const result = await planningService.getBalanceAnalysis(userId);\r\n\r\n      if (result.success) {\r\n        set({ balanceAnalysis: result.data, loading: false });\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to analyze balance',\r\n          loading: false\r\n        });\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze postponed tasks\r\n  analyzePostponedTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { tasks, planningService } = get();\r\n      const userTasks = tasks.filter(task => task.userId === userId);\r\n      const result = await planningService.getPostponedTasksAnalysis(userTasks);\r\n\r\n      if (result.success) {\r\n        set({ postponedAlerts: result.data.alerts, loading: false });\r\n      } else {\r\n        set({\r\n          error: result.error || 'Failed to analyze postponed tasks',\r\n          loading: false\r\n        });\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => set({ loading }),\r\n  setError: (error: string | null) => set({ error }),\r\n  clearError: () => set({ error: null })\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AAAA;AACA;AAAA;;;;;AAoCO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,gBAAgB;QAChB,OAAO,EAAE;QACT,eAAe;QACf,iBAAiB;QACjB,iBAAiB,EAAE;QACnB,SAAS;QACT,OAAO;QAEP,oBAAoB;QACpB,iBAAiB,CAAA,GAAA,kKAAA,CAAA,4BAAyB,AAAD;QACzC,aAAa,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD;QAEjC,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,YAAY,CAAC;gBAE9C,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBAAE,OAAO,OAAO,IAAI;wBAAE,SAAS;oBAAM;gBAC3C,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,UAAU,CAAC;gBAE5C,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO;gCAAC,OAAO,IAAI;mCAAK,MAAM,KAAK;6BAAC;4BACpC,SAAS;wBACX,CAAC;gBACH,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;oBACA,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO,IAAY;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,UAAU,CAAC;oBAAE;oBAAI,GAAG,OAAO;gBAAC;gBAE7D,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KAAK,OAAO,IAAI,GAAG;4BAEjC,SAAS;wBACX,CAAC;gBACH,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;oBACA,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,UAAU,CAAC;gBAE5C,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;4BAC9C,SAAS;wBACX,CAAC;gBACH,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;oBACA,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY,gBAAyB;YACxD,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG;gBACzC,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE5C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,mCAAmC;gBACnC,MAAM,SAAS,MAAM,YAAY,YAAY,CAAC;oBAC5C;oBACA;oBACA,mBAAmB;gBACrB;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,qBAAqB;oBACrB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,IACrB,EAAE,EAAE,KAAK,KAAK,OAAO,IAAI,GAAG;wBAEhC,CAAC;oBAED,qBAAqB;oBACrB,MAAM,gBAAgB,mBAAmB,CACvC,KAAK,MAAM,EACX,KAAK,QAAQ,EACb,kBAAkB,KAAK,iBAAiB;gBAE5C,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YAEF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY;YAC/B,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,YAAY,CAAC;oBAC5C;oBACA;gBACF;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,qBAAqB;oBACrB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,IACrB,EAAE,EAAE,KAAK,KAAK,OAAO,IAAI,GAAG;wBAEhC,CAAC;gBACH,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,aAAa;QACb,WAAW,OAAO;YAChB,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,SAAS,CAAC;gBAE3C,IAAI,OAAO,OAAO,EAAE;oBAClB,qBAAqB;oBACrB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,IACrB,EAAE,EAAE,KAAK,KAAK,OAAO,IAAI,GAAG;wBAEhC,CAAC;gBACH,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,aAAa;QACb,WAAW,OAAO;YAChB,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM,SAAS,MAAM,YAAY,SAAS,CAAC;gBAE3C,IAAI,OAAO,OAAO,EAAE;oBAClB,qBAAqB;oBACrB,IAAI,CAAA,QAAS,CAAC;4BACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,IACrB,EAAE,EAAE,KAAK,KAAK,OAAO,IAAI,GAAG;wBAEhC,CAAC;gBACH,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG;gBACnC,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBAEvD,WAAW;gBACX,IAAI,iBAAiB;gBACrB,IAAI;oBACF,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,iBACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,QACT,MAAM;oBAET,iBAAiB,aAAa;gBAChC,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;gBACd;gBAEA,WAAW;gBACX,MAAM,SAAS,MAAM,gBAAgB,iBAAiB,CAAC,QAAQ,WAAW;gBAE1E,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBACF,eAAe,OAAO,IAAI,CAAC,QAAQ;wBACnC,SAAS;oBACX;gBACF,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,eAAe,EAAE,GAAG;gBAC5B,MAAM,SAAS,MAAM,gBAAgB,kBAAkB,CAAC;gBAExD,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBAAE,iBAAiB,OAAO,IAAI;wBAAE,SAAS;oBAAM;gBACrD,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG;gBACnC,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBACvD,MAAM,SAAS,MAAM,gBAAgB,yBAAyB,CAAC;gBAE/D,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBAAE,iBAAiB,OAAO,IAAI,CAAC,MAAM;wBAAE,SAAS;oBAAM;gBAC5D,OAAO;oBACL,IAAI;wBACF,OAAO,OAAO,KAAK,IAAI;wBACvB,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC,UAAqB,IAAI;gBAAE;YAAQ;QAChD,UAAU,CAAC,QAAyB,IAAI;gBAAE;YAAM;QAChD,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC", "debugId": null}}, {"offset": {"line": 4093, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/tasks/TaskItem.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Clock, \n  Calendar, \n  Edit, \n  Trash2, \n  Play, \n  Pause, \n  CheckCircle,\n  AlertCircle,\n  MoreVertical\n} from 'lucide-react';\nimport { Task } from '@/shared';\nimport { \n  getCategoryColor, \n  getCategoryIcon, \n  getCategoryName,\n  getStatusColor,\n  getStatusName,\n  getPriorityColor,\n  isTaskOverdue,\n  isTaskDueSoon\n} from '@/shared/utils/taskUtils';\n\ninterface TaskItemProps {\n  task: Task;\n  onEdit: (task: Task) => void;\n  onDelete: (taskId: string) => void;\n  onStatusChange: (taskId: string, status: Task['status']) => void;\n  onStart: (taskId: string) => void;\n  onComplete: (taskId: string) => void;\n}\n\nexport default function TaskItem({ \n  task, \n  onEdit, \n  onDelete, \n  onStatusChange, \n  onStart, \n  onComplete \n}: TaskItemProps) {\n  const [showActions, setShowActions] = useState(false);\n  \n  const categoryColor = getCategoryColor(task.category);\n  const statusColor = getStatusColor(task.status);\n  const isOverdue = isTaskOverdue(task);\n  const isDueSoon = isTaskDueSoon(task);\n  \n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h ${mins}m`;\n    }\n    return `${mins}m`;\n  };\n\n  const formatDeadline = (date: Date) => {\n    const now = new Date();\n    const diffTime = date.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays < 0) {\n      return `逾期 ${Math.abs(diffDays)} 天`;\n    } else if (diffDays === 0) {\n      return '今天到期';\n    } else if (diffDays === 1) {\n      return '明天到期';\n    } else if (diffDays <= 7) {\n      return `${diffDays} 天后到期`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  const getQuadrantInfo = (importance: number, urgency: number) => {\n    if (importance >= 4 && urgency >= 4) {\n      return { label: 'Q1', color: 'bg-red-100 text-red-800', desc: '重要紧急' };\n    } else if (importance >= 4 && urgency < 4) {\n      return { label: 'Q2', color: 'bg-blue-100 text-blue-800', desc: '重要不紧急' };\n    } else if (importance < 4 && urgency >= 4) {\n      return { label: 'Q3', color: 'bg-yellow-100 text-yellow-800', desc: '不重要紧急' };\n    } else {\n      return { label: 'Q4', color: 'bg-gray-100 text-gray-800', desc: '不重要不紧急' };\n    }\n  };\n\n  const quadrant = getQuadrantInfo(task.importance, task.urgency);\n\n  const handleStatusChange = (newStatus: Task['status']) => {\n    if (newStatus === 'completed') {\n      onComplete(task.id);\n    } else if (newStatus === 'in-progress') {\n      onStart(task.id);\n    } else {\n      onStatusChange(task.id, newStatus);\n    }\n  };\n\n  return (\n    <div className={`\n      bg-white rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow duration-200\n      ${isOverdue ? 'border-l-red-500 bg-red-50' : \n        isDueSoon ? 'border-l-yellow-500 bg-yellow-50' : \n        `border-l-${categoryColor}-500`}\n    `}>\n      <div className=\"p-4\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center gap-2 mb-1\">\n              <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                {task.title}\n              </h3>\n              <span className={`px-2 py-1 text-xs font-medium rounded-full ${quadrant.color}`}>\n                {quadrant.label}\n              </span>\n            </div>\n            {task.description && (\n              <p className=\"text-sm text-gray-600 line-clamp-2\">\n                {task.description}\n              </p>\n            )}\n          </div>\n          \n          {/* Actions Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowActions(!showActions)}\n              className=\"p-1 rounded-full hover:bg-gray-100 transition-colors\"\n            >\n              <MoreVertical className=\"h-4 w-4 text-gray-500\" />\n            </button>\n            \n            {showActions && (\n              <div className=\"absolute right-0 top-8 bg-white rounded-md shadow-lg border z-10 min-w-[120px]\">\n                <button\n                  onClick={() => {\n                    onEdit(task);\n                    setShowActions(false);\n                  }}\n                  className=\"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\"\n                >\n                  <Edit className=\"h-3 w-3\" />\n                  编辑\n                </button>\n                <button\n                  onClick={() => {\n                    onDelete(task.id);\n                    setShowActions(false);\n                  }}\n                  className=\"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  删除\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Task Info */}\n        <div className=\"flex items-center gap-4 text-sm text-gray-600 mb-3\">\n          <div className=\"flex items-center gap-1\">\n            <span className={`w-2 h-2 rounded-full bg-${categoryColor}-500`}></span>\n            <span>{getCategoryName(task.category)}</span>\n          </div>\n          \n          <div className=\"flex items-center gap-1\">\n            <Clock className=\"h-3 w-3\" />\n            <span>{formatDuration(task.estimatedDuration)}</span>\n          </div>\n          \n          <div className={`flex items-center gap-1 ${isOverdue ? 'text-red-600' : isDueSoon ? 'text-yellow-600' : ''}`}>\n            <Calendar className=\"h-3 w-3\" />\n            <span>{formatDeadline(task.deadline)}</span>\n            {isOverdue && <AlertCircle className=\"h-3 w-3\" />}\n          </div>\n        </div>\n\n        {/* Status and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}>\n              {getStatusName(task.status)}\n            </span>\n            {task.postponeCount > 0 && (\n              <span className=\"px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">\n                推迟 {task.postponeCount} 次\n              </span>\n            )}\n          </div>\n          \n          {/* Quick Actions */}\n          <div className=\"flex items-center gap-1\">\n            {task.status === 'pending' && (\n              <button\n                onClick={() => handleStatusChange('in-progress')}\n                className=\"p-1 rounded-full hover:bg-green-100 text-green-600 transition-colors\"\n                title=\"开始任务\"\n              >\n                <Play className=\"h-4 w-4\" />\n              </button>\n            )}\n            \n            {task.status === 'in-progress' && (\n              <>\n                <button\n                  onClick={() => handleStatusChange('pending')}\n                  className=\"p-1 rounded-full hover:bg-yellow-100 text-yellow-600 transition-colors\"\n                  title=\"暂停任务\"\n                >\n                  <Pause className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => handleStatusChange('completed')}\n                  className=\"p-1 rounded-full hover:bg-blue-100 text-blue-600 transition-colors\"\n                  title=\"完成任务\"\n                >\n                  <CheckCircle className=\"h-4 w-4\" />\n                </button>\n              </>\n            )}\n            \n            {task.status === 'completed' && (\n              <button\n                onClick={() => handleStatusChange('pending')}\n                className=\"p-1 rounded-full hover:bg-gray-100 text-gray-600 transition-colors\"\n                title=\"重新激活\"\n              >\n                <Play className=\"h-4 w-4\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAfA;;;;;AAmCe,SAAS,SAAS,EAC/B,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,cAAc,EACd,OAAO,EACP,UAAU,EACI;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ;IACpD,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;IAC9C,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;IAChC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;IAEhC,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,OAAO,UAAU;QACvB,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7B;QACA,OAAO,GAAG,KAAK,CAAC,CAAC;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,OAAO,KAAK,IAAI,OAAO;QAC7C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,WAAW,GAAG;YAChB,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,UAAU,EAAE,CAAC;QACrC,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO;QACT,OAAO,IAAI,YAAY,GAAG;YACxB,OAAO,GAAG,SAAS,KAAK,CAAC;QAC3B,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,IAAI,cAAc,KAAK,WAAW,GAAG;YACnC,OAAO;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,MAAM;YAAO;QACvE,OAAO,IAAI,cAAc,KAAK,UAAU,GAAG;YACzC,OAAO;gBAAE,OAAO;gBAAM,OAAO;gBAA6B,MAAM;YAAQ;QAC1E,OAAO,IAAI,aAAa,KAAK,WAAW,GAAG;YACzC,OAAO;gBAAE,OAAO;gBAAM,OAAO;gBAAiC,MAAM;YAAQ;QAC9E,OAAO;YACL,OAAO;gBAAE,OAAO;gBAAM,OAAO;gBAA6B,MAAM;YAAS;QAC3E;IACF;IAEA,MAAM,WAAW,gBAAgB,KAAK,UAAU,EAAE,KAAK,OAAO;IAE9D,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,aAAa;YAC7B,WAAW,KAAK,EAAE;QACpB,OAAO,IAAI,cAAc,eAAe;YACtC,QAAQ,KAAK,EAAE;QACjB,OAAO;YACL,eAAe,KAAK,EAAE,EAAE;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC;;MAEf,EAAE,YAAY,+BACZ,YAAY,qCACZ,CAAC,SAAS,EAAE,cAAc,IAAI,CAAC,CAAC;IACpC,CAAC;kBACC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAK,WAAW,CAAC,2CAA2C,EAAE,SAAS,KAAK,EAAE;sDAC5E,SAAS,KAAK;;;;;;;;;;;;gCAGlB,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;sCAMvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;8CAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;gCAGzB,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,OAAO;gDACP,eAAe;4CACjB;4CACA,WAAU;;8DAEV,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG9B,8OAAC;4CACC,SAAS;gDACP,SAAS,KAAK,EAAE;gDAChB,eAAe;4CACjB;4CACA,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;8BASxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,wBAAwB,EAAE,cAAc,IAAI,CAAC;;;;;;8CAC/D,8OAAC;8CAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAM,eAAe,KAAK,iBAAiB;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAW,CAAC,wBAAwB,EAAE,YAAY,iBAAiB,YAAY,oBAAoB,IAAI;;8CAC1G,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,eAAe,KAAK,QAAQ;;;;;;gCAClC,2BAAa,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAAE,aAAa;8CACzE,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,MAAM;;;;;;gCAE3B,KAAK,aAAa,GAAG,mBACpB,8OAAC;oCAAK,WAAU;;wCAA+D;wCACzE,KAAK,aAAa;wCAAC;;;;;;;;;;;;;sCAM7B,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,MAAM,KAAK,2BACf,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAInB,KAAK,MAAM,KAAK,+BACf;;sDACE,8OAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;gCAK5B,KAAK,MAAM,KAAK,6BACf,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 4532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/QuadrantSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Priority, Quadrant, classifyQuadrant, getQuadrantDescription } from '@/shared';\n\ninterface QuadrantSelectorProps {\n  importance: Priority;\n  urgency: Priority;\n  onChange: (importance: Priority, urgency: Priority) => void;\n}\n\nexport default function QuadrantSelector({ importance, urgency, onChange }: QuadrantSelectorProps) {\n  const [selectedQuadrant, setSelectedQuadrant] = useState<number | null>(null);\n\n  const currentQuadrant = classifyQuadrant(importance, urgency);\n\n  // 象限配置\n  const quadrants = [\n    {\n      id: 1,\n      title: '重要且紧急',\n      description: '立即处理',\n      color: 'bg-red-100 border-red-300 hover:bg-red-200',\n      selectedColor: 'bg-red-200 border-red-500',\n      textColor: 'text-red-800',\n      importance: 5,\n      urgency: 5,\n      position: 'top-right'\n    },\n    {\n      id: 2,\n      title: '重要不紧急',\n      description: '计划安排',\n      color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',\n      selectedColor: 'bg-blue-200 border-blue-500',\n      textColor: 'text-blue-800',\n      importance: 5,\n      urgency: 2,\n      position: 'top-left'\n    },\n    {\n      id: 3,\n      title: '不重要但紧急',\n      description: '委托处理',\n      color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',\n      selectedColor: 'bg-yellow-200 border-yellow-500',\n      textColor: 'text-yellow-800',\n      importance: 2,\n      urgency: 5,\n      position: 'bottom-right'\n    },\n    {\n      id: 4,\n      title: '不重要不紧急',\n      description: '减少或删除',\n      color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',\n      selectedColor: 'bg-gray-200 border-gray-500',\n      textColor: 'text-gray-800',\n      importance: 2,\n      urgency: 2,\n      position: 'bottom-left'\n    }\n  ];\n\n  const handleQuadrantClick = (quadrant: typeof quadrants[0]) => {\n    setSelectedQuadrant(quadrant.id);\n    onChange(quadrant.importance, quadrant.urgency);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"text-sm font-medium text-gray-700 mb-2\">\n        任务优先级（点击象限选择）\n      </div>\n      \n      {/* 四象限网格 */}\n      <div className=\"relative w-full max-w-md mx-auto\">\n        {/* 坐标轴标签 */}\n        <div className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium\">\n          紧急程度 →\n        </div>\n        <div className=\"absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium\">\n          重要程度 →\n        </div>\n        \n        {/* 象限网格 */}\n        <div className=\"grid grid-cols-2 gap-2 w-80 h-80\">\n          {/* 第二象限：重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}\n              ${quadrants[1].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[1])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">计划安排</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 II</div>\n          </div>\n\n          {/* 第一象限：重要且紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}\n              ${quadrants[0].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[0])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">立即处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 I</div>\n          </div>\n\n          {/* 第四象限：不重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}\n              ${quadrants[3].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[3])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">减少删除</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 IV</div>\n          </div>\n\n          {/* 第三象限：不重要但紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}\n              ${quadrants[2].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[2])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">委托处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 III</div>\n          </div>\n        </div>\n\n        {/* 当前选择显示 */}\n        <div className=\"mt-4 text-center\">\n          <div className=\"text-sm text-gray-600\">\n            当前选择：<span className=\"font-medium\">象限 {currentQuadrant}</span>\n          </div>\n          <div className=\"text-xs text-gray-500 mt-1\">\n            重要性: {importance}/5 | 紧急性: {urgency}/5\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAWe,SAAS,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAyB;IAC/F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,kBAAkB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;IAErD,OAAO;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,SAAS,EAAE;QAC/B,SAAS,SAAS,UAAU,EAAE,SAAS,OAAO;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAyC;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCAAwF;;;;;;kCAGvG,8OAAC;wBAAI,WAAU;kCAAoG;;;;;;kCAKnH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAwB;kDAChC,8OAAC;wCAAK,WAAU;;4CAAc;4CAAI;;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;oCAA6B;oCACpC;oCAAW;oCAAW;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 4887, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/tasks/TaskEditForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { X, Save, Calendar, Clock } from 'lucide-react';\nimport { Task, TaskCategory, Priority } from '@/shared';\nimport QuadrantSelector from '@/components/QuadrantSelector';\n\ninterface TaskEditFormProps {\n  task: Task | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (taskData: Partial<Task>) => Promise<void>;\n}\n\ninterface FormData {\n  title: string;\n  description: string;\n  category: TaskCategory;\n  importance: Priority;\n  urgency: Priority;\n  deadline: string;\n  estimatedDuration: number; // 以小时为单位显示\n}\n\nexport default function TaskEditForm({ task, isOpen, onClose, onSave }: TaskEditFormProps) {\n  const [formData, setFormData] = useState<FormData>({\n    title: '',\n    description: '',\n    category: 'work',\n    importance: 3,\n    urgency: 3,\n    deadline: '',\n    estimatedDuration: 1\n  });\n  \n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // 当任务数据变化时更新表单\n  useEffect(() => {\n    if (task) {\n      setFormData({\n        title: task.title,\n        description: task.description || '',\n        category: task.category,\n        importance: task.importance,\n        urgency: task.urgency,\n        deadline: task.deadline.toISOString().slice(0, 16), // 格式化为 datetime-local\n        estimatedDuration: Math.round(task.estimatedDuration / 60 * 10) / 10 // 转换为小时并保留一位小数\n      });\n    }\n  }, [task]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: name === 'importance' || name === 'urgency' ? parseInt(value) as Priority :\n              name === 'estimatedDuration' ? parseFloat(value) :\n              value\n    }));\n    \n    // 清除对应字段的错误\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const handleQuadrantChange = (importance: Priority, urgency: Priority) => {\n    setFormData(prev => ({\n      ...prev,\n      importance,\n      urgency\n    }));\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = '任务标题不能为空';\n    }\n\n    if (!formData.deadline) {\n      newErrors.deadline = '截止时间不能为空';\n    } else {\n      const deadlineDate = new Date(formData.deadline);\n      const now = new Date();\n      if (deadlineDate <= now) {\n        newErrors.deadline = '截止时间必须晚于当前时间';\n      }\n    }\n\n    if (formData.estimatedDuration <= 0) {\n      newErrors.estimatedDuration = '预估时长必须大于0';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const updateData: Partial<Task> = {\n        title: formData.title.trim(),\n        description: formData.description.trim(),\n        category: formData.category,\n        importance: formData.importance,\n        urgency: formData.urgency,\n        deadline: new Date(formData.deadline),\n        estimatedDuration: Math.round(formData.estimatedDuration * 60) // 转换为分钟\n      };\n\n      await onSave(updateData);\n      onClose();\n    } catch (error) {\n      console.error('Failed to save task:', error);\n      setErrors({ submit: '保存失败，请重试' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            {task ? '编辑任务' : '新建任务'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X className=\"h-5 w-5 text-gray-500\" />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* 任务标题 */}\n          <div>\n            <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              任务标题 *\n            </label>\n            <input\n              type=\"text\"\n              id=\"title\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.title ? 'border-red-300' : 'border-gray-300'\n              }`}\n              placeholder=\"请输入任务标题\"\n            />\n            {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n          </div>\n\n          {/* 任务描述 */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              任务描述\n            </label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              rows={3}\n              value={formData.description}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"请输入任务描述（可选）\"\n            />\n          </div>\n\n          {/* 任务分类 */}\n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              任务分类 *\n            </label>\n            <select\n              id=\"category\"\n              name=\"category\"\n              value={formData.category}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"work\">工作</option>\n              <option value=\"improvement\">自我提升</option>\n              <option value=\"entertainment\">娱乐</option>\n            </select>\n          </div>\n\n          {/* 四象限优先级选择器 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              优先级设置 *\n            </label>\n            <QuadrantSelector\n              importance={formData.importance}\n              urgency={formData.urgency}\n              onChange={handleQuadrantChange}\n            />\n          </div>\n\n          {/* 截止时间 */}\n          <div>\n            <label htmlFor=\"deadline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              截止时间 *\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"datetime-local\"\n                id=\"deadline\"\n                name=\"deadline\"\n                value={formData.deadline}\n                onChange={handleChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.deadline ? 'border-red-300' : 'border-gray-300'\n                }`}\n              />\n              <Calendar className=\"absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none\" />\n            </div>\n            {errors.deadline && <p className=\"mt-1 text-sm text-red-600\">{errors.deadline}</p>}\n          </div>\n\n          {/* 预估时长 */}\n          <div>\n            <label htmlFor=\"estimatedDuration\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              预估时长（小时）*\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"number\"\n                id=\"estimatedDuration\"\n                name=\"estimatedDuration\"\n                min=\"0.1\"\n                step=\"0.1\"\n                value={formData.estimatedDuration}\n                onChange={handleChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.estimatedDuration ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"1.0\"\n              />\n              <Clock className=\"absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none\" />\n            </div>\n            {errors.estimatedDuration && <p className=\"mt-1 text-sm text-red-600\">{errors.estimatedDuration}</p>}\n          </div>\n\n          {/* 提交错误 */}\n          {errors.submit && (\n            <div className=\"p-3 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-sm text-red-600\">{errors.submit}</p>\n            </div>\n          )}\n\n          {/* 操作按钮 */}\n          <div className=\"flex justify-end gap-3 pt-4 border-t\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              取消\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n            >\n              {loading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  保存中...\n                </>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4\" />\n                  保存\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAwBe,SAAS,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAqB;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,SAAS;QACT,UAAU;QACV,mBAAmB;IACrB;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW,IAAI;gBACjC,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG;gBAC/C,mBAAmB,KAAK,KAAK,CAAC,KAAK,iBAAiB,GAAG,KAAK,MAAM,GAAG,eAAe;YACtF;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,gBAAgB,SAAS,YAAY,SAAS,SACvD,SAAS,sBAAsB,WAAW,SAC1C;YACV,CAAC;QAED,YAAY;QACZ,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,uBAAuB,CAAC,YAAsB;QAClD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA;YACF,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,QAAQ;YAC/C,MAAM,MAAM,IAAI;YAChB,IAAI,gBAAgB,KAAK;gBACvB,UAAU,QAAQ,GAAG;YACvB;QACF;QAEA,IAAI,SAAS,iBAAiB,IAAI,GAAG;YACnC,UAAU,iBAAiB,GAAG;QAChC;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,aAA4B;gBAChC,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,UAAU,IAAI,KAAK,SAAS,QAAQ;gBACpC,mBAAmB,KAAK,KAAK,CAAC,SAAS,iBAAiB,GAAG,IAAI,QAAQ;YACzE;YAEA,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,UAAU;gBAAE,QAAQ;YAAW;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,OAAO,SAAS;;;;;;sCAEnB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAW,CAAC,iGAAiG,EAC3G,OAAO,KAAK,GAAG,mBAAmB,mBAClC;oCACF,aAAY;;;;;;gCAEb,OAAO,KAAK,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;sCAIzE,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAM;oCACN,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC,sIAAA,CAAA,UAAgB;oCACf,YAAY,SAAS,UAAU;oCAC/B,SAAS,SAAS,OAAO;oCACzB,UAAU;;;;;;;;;;;;sCAKd,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAW,CAAC,iGAAiG,EAC3G,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;;;;;sDAEJ,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;gCAErB,OAAO,QAAQ,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,QAAQ;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAoB,WAAU;8CAA+C;;;;;;8CAG5F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU;4CACV,WAAW,CAAC,iGAAiG,EAC3G,OAAO,iBAAiB,GAAG,mBAAmB,mBAC9C;4CACF,aAAY;;;;;;sDAEd,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;gCAElB,OAAO,iBAAiB,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,iBAAiB;;;;;;;;;;;;wBAIhG,OAAO,MAAM,kBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,MAAM;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAkE;;qEAInF;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 5386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/tasks/TaskDeleteConfirm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { AlertTriangle, Trash2, X } from 'lucide-react';\nimport { Task } from '@/shared';\n\ninterface TaskDeleteConfirmProps {\n  task: Task | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: (taskId: string) => Promise<void>;\n}\n\nexport default function TaskDeleteConfirm({ \n  task, \n  isOpen, \n  onClose, \n  onConfirm \n}: TaskDeleteConfirmProps) {\n  const [loading, setLoading] = useState(false);\n\n  const handleConfirm = async () => {\n    if (!task) return;\n    \n    setLoading(true);\n    try {\n      await onConfirm(task.id);\n      onClose();\n    } catch (error) {\n      console.error('Failed to delete task:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isOpen || !task) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center\">\n              <AlertTriangle className=\"h-5 w-5 text-red-600\" />\n            </div>\n            <h2 className=\"text-lg font-semibold text-gray-900\">\n              删除任务\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X className=\"h-5 w-5 text-gray-500\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          <p className=\"text-gray-700 mb-4\">\n            您确定要删除任务 <span className=\"font-medium text-gray-900\">\"{task.title}\"</span> 吗？\n          </p>\n          \n          <div className=\"bg-red-50 border border-red-200 rounded-md p-3 mb-4\">\n            <div className=\"flex\">\n              <AlertTriangle className=\"h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0\" />\n              <div className=\"text-sm text-red-700\">\n                <p className=\"font-medium mb-1\">此操作无法撤销</p>\n                <p>删除后，任务的所有相关数据将永久丢失，包括：</p>\n                <ul className=\"list-disc list-inside mt-1 space-y-1\">\n                  <li>任务详细信息</li>\n                  <li>历史记录</li>\n                  <li>相关统计数据</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Task Info */}\n          <div className=\"bg-gray-50 rounded-md p-3 mb-6\">\n            <h4 className=\"text-sm font-medium text-gray-900 mb-2\">任务信息</h4>\n            <div className=\"space-y-1 text-sm text-gray-600\">\n              <div className=\"flex justify-between\">\n                <span>标题：</span>\n                <span className=\"font-medium\">{task.title}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>分类：</span>\n                <span className=\"font-medium\">\n                  {task.category === 'work' ? '工作' : \n                   task.category === 'improvement' ? '自我提升' : '娱乐'}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>状态：</span>\n                <span className=\"font-medium\">\n                  {task.status === 'pending' ? '未开始' :\n                   task.status === 'in-progress' ? '进行中' :\n                   task.status === 'completed' ? '已完成' : '已推迟'}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>截止时间：</span>\n                <span className=\"font-medium\">\n                  {task.deadline.toLocaleDateString('zh-CN')}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex justify-end gap-3 p-6 border-t bg-gray-50\">\n          <button\n            type=\"button\"\n            onClick={onClose}\n            disabled={loading}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\"\n          >\n            取消\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleConfirm}\n            disabled={loading}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n          >\n            {loading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                删除中...\n              </>\n            ) : (\n              <>\n                <Trash2 className=\"h-4 w-4\" />\n                确认删除\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAae,SAAS,kBAAkB,EACxC,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACc;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,UAAU,KAAK,EAAE;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO;IAE7B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAItD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAqB;8CACvB,8OAAC;oCAAK,WAAU;;wCAA4B;wCAAE,KAAK,KAAK;wCAAC;;;;;;;gCAAQ;;;;;;;sCAG5E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAAe,KAAK,KAAK;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ,KAAK,SAAS,OAC3B,KAAK,QAAQ,KAAK,gBAAgB,SAAS;;;;;;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,KAAK,MAAM,KAAK,YAAY,QAC5B,KAAK,MAAM,KAAK,gBAAgB,QAChC,KAAK,MAAM,KAAK,cAAc,QAAQ;;;;;;;;;;;;sDAG3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,wBACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAkE;;6DAInF;;kDACE,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 5775, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/tasks/TaskList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { Search, Filter, Plus, SortAsc, SortDesc } from 'lucide-react';\nimport { Task, TaskStatus, TaskCategory } from '@/shared';\nimport TaskItem from './TaskItem';\nimport TaskEditForm from './TaskEditForm';\nimport TaskDeleteConfirm from './TaskDeleteConfirm';\n\ninterface TaskListProps {\n  tasks: Task[];\n  loading?: boolean;\n  onCreateTask: () => void;\n  onEditTask: (taskId: string, updates: Partial<Task>) => Promise<void>;\n  onDeleteTask: (taskId: string) => Promise<void>;\n  onStatusChange: (taskId: string, status: TaskStatus) => Promise<void>;\n  onStartTask: (taskId: string) => Promise<void>;\n  onCompleteTask: (taskId: string) => Promise<void>;\n}\n\ntype SortOption = 'deadline' | 'priority' | 'created' | 'title';\ntype SortDirection = 'asc' | 'desc';\n\ninterface FilterOptions {\n  status: TaskStatus | 'all';\n  category: TaskCategory | 'all';\n  search: string;\n}\n\nexport default function TaskList({\n  tasks,\n  loading = false,\n  onCreateTask,\n  onEditTask,\n  onDeleteTask,\n  onStatusChange,\n  onStartTask,\n  onCompleteTask\n}: TaskListProps) {\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\n  const [deletingTask, setDeletingTask] = useState<Task | null>(null);\n  const [showEditForm, setShowEditForm] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  \n  const [filters, setFilters] = useState<FilterOptions>({\n    status: 'all',\n    category: 'all',\n    search: ''\n  });\n  \n  const [sortBy, setSortBy] = useState<SortOption>('deadline');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // 过滤和排序任务\n  const filteredAndSortedTasks = useMemo(() => {\n    let filtered = tasks.filter(task => {\n      // 状态过滤\n      if (filters.status !== 'all' && task.status !== filters.status) {\n        return false;\n      }\n      \n      // 分类过滤\n      if (filters.category !== 'all' && task.category !== filters.category) {\n        return false;\n      }\n      \n      // 搜索过滤\n      if (filters.search) {\n        const searchLower = filters.search.toLowerCase();\n        return task.title.toLowerCase().includes(searchLower) ||\n               (task.description && task.description.toLowerCase().includes(searchLower));\n      }\n      \n      return true;\n    });\n\n    // 排序\n    filtered.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortBy) {\n        case 'deadline':\n          comparison = a.deadline.getTime() - b.deadline.getTime();\n          break;\n        case 'priority':\n          // 按重要性和紧急性的综合分数排序\n          const scoreA = a.importance * 2 + a.urgency;\n          const scoreB = b.importance * 2 + b.urgency;\n          comparison = scoreB - scoreA;\n          break;\n        case 'created':\n          comparison = a.createdAt.getTime() - b.createdAt.getTime();\n          break;\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n      }\n      \n      return sortDirection === 'asc' ? comparison : -comparison;\n    });\n\n    return filtered;\n  }, [tasks, filters, sortBy, sortDirection]);\n\n  const handleEdit = (task: Task) => {\n    setEditingTask(task);\n    setShowEditForm(true);\n  };\n\n  const handleDelete = (taskId: string) => {\n    const task = tasks.find(t => t.id === taskId);\n    if (task) {\n      setDeletingTask(task);\n      setShowDeleteConfirm(true);\n    }\n  };\n\n  const handleSaveEdit = async (updates: Partial<Task>) => {\n    if (editingTask) {\n      await onEditTask(editingTask.id, updates);\n    }\n  };\n\n  const handleSort = (option: SortOption) => {\n    if (sortBy === option) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(option);\n      setSortDirection('asc');\n    }\n  };\n\n  const getStatusCount = (status: TaskStatus) => {\n    return tasks.filter(task => task.status === status).length;\n  };\n\n  const getCategoryCount = (category: TaskCategory) => {\n    return tasks.filter(task => task.category === category).length;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">任务管理</h1>\n          <p className=\"text-gray-600\">共 {tasks.length} 个任务，{filteredAndSortedTasks.length} 个符合条件</p>\n        </div>\n        <button\n          onClick={onCreateTask}\n          className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 gap-2\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          新建任务\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow p-4\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"搜索任务标题或描述...\"\n                value={filters.search}\n                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* Filter Toggle */}\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 gap-2\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            筛选\n          </button>\n        </div>\n\n        {/* Filters */}\n        {showFilters && (\n          <div className=\"mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            {/* Status Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">状态</label>\n              <select\n                value={filters.status}\n                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as TaskStatus | 'all' }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">全部 ({tasks.length})</option>\n                <option value=\"pending\">未开始 ({getStatusCount('pending')})</option>\n                <option value=\"in-progress\">进行中 ({getStatusCount('in-progress')})</option>\n                <option value=\"completed\">已完成 ({getStatusCount('completed')})</option>\n                <option value=\"postponed\">已推迟 ({getStatusCount('postponed')})</option>\n              </select>\n            </div>\n\n            {/* Category Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">分类</label>\n              <select\n                value={filters.category}\n                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value as TaskCategory | 'all' }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">全部 ({tasks.length})</option>\n                <option value=\"work\">工作 ({getCategoryCount('work')})</option>\n                <option value=\"improvement\">自我提升 ({getCategoryCount('improvement')})</option>\n                <option value=\"entertainment\">娱乐 ({getCategoryCount('entertainment')})</option>\n              </select>\n            </div>\n\n            {/* Sort Options */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">排序</label>\n              <div className=\"flex gap-1\">\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value as SortOption)}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"deadline\">截止时间</option>\n                  <option value=\"priority\">优先级</option>\n                  <option value=\"created\">创建时间</option>\n                  <option value=\"title\">标题</option>\n                </select>\n                <button\n                  onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}\n                  className=\"px-2 py-2 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-50\"\n                >\n                  {sortDirection === 'asc' ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />}\n                </button>\n              </div>\n            </div>\n\n            {/* Clear Filters */}\n            <div className=\"flex items-end\">\n              <button\n                onClick={() => {\n                  setFilters({ status: 'all', category: 'all', search: '' });\n                  setSortBy('deadline');\n                  setSortDirection('asc');\n                }}\n                className=\"w-full px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50\"\n              >\n                清除筛选\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Task List */}\n      <div className=\"space-y-4\">\n        {loading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"text-gray-500 mt-2\">正在加载任务...</p>\n          </div>\n        ) : filteredAndSortedTasks.length === 0 ? (\n          <div className=\"text-center py-12 bg-white rounded-lg shadow\">\n            <p className=\"text-gray-500 text-lg\">\n              {filters.search || filters.status !== 'all' || filters.category !== 'all' \n                ? '没有找到符合条件的任务' \n                : '还没有任务，点击上方按钮创建第一个任务吧！'}\n            </p>\n          </div>\n        ) : (\n          filteredAndSortedTasks.map(task => (\n            <TaskItem\n              key={task.id}\n              task={task}\n              onEdit={handleEdit}\n              onDelete={handleDelete}\n              onStatusChange={onStatusChange}\n              onStart={onStartTask}\n              onComplete={onCompleteTask}\n            />\n          ))\n        )}\n      </div>\n\n      {/* Edit Form Modal */}\n      <TaskEditForm\n        task={editingTask}\n        isOpen={showEditForm}\n        onClose={() => {\n          setShowEditForm(false);\n          setEditingTask(null);\n        }}\n        onSave={handleSaveEdit}\n      />\n\n      {/* Delete Confirmation Modal */}\n      <TaskDeleteConfirm\n        task={deletingTask}\n        isOpen={showDeleteConfirm}\n        onClose={() => {\n          setShowDeleteConfirm(false);\n          setDeletingTask(null);\n        }}\n        onConfirm={onDeleteTask}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;;AA6Be,SAAS,SAAS,EAC/B,KAAK,EACL,UAAU,KAAK,EACf,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,cAAc,EACd,WAAW,EACX,cAAc,EACA;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,UAAU;IACV,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,IAAI,WAAW,MAAM,MAAM,CAAC,CAAA;YAC1B,OAAO;YACP,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;gBAC9D,OAAO;YACT;YAEA,OAAO;YACP,IAAI,QAAQ,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,QAAQ,EAAE;gBACpE,OAAO;YACT;YAEA,OAAO;YACP,IAAI,QAAQ,MAAM,EAAE;gBAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;gBAC9C,OAAO,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;YACtE;YAEA,OAAO;QACT;QAEA,KAAK;QACL,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,aAAa;YAEjB,OAAQ;gBACN,KAAK;oBACH,aAAa,EAAE,QAAQ,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO;oBACtD;gBACF,KAAK;oBACH,kBAAkB;oBAClB,MAAM,SAAS,EAAE,UAAU,GAAG,IAAI,EAAE,OAAO;oBAC3C,MAAM,SAAS,EAAE,UAAU,GAAG,IAAI,EAAE,OAAO;oBAC3C,aAAa,SAAS;oBACtB;gBACF,KAAK;oBACH,aAAa,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxD;gBACF,KAAK;oBACH,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;oBAC1C;YACJ;YAEA,OAAO,kBAAkB,QAAQ,aAAa,CAAC;QACjD;QAEA,OAAO;IACT,GAAG;QAAC;QAAO;QAAS;QAAQ;KAAc;IAE1C,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,IAAI,MAAM;YACR,gBAAgB;YAChB,qBAAqB;QACvB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,aAAa;YACf,MAAM,WAAW,YAAY,EAAE,EAAE;QACnC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,QAAQ;YACrB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,UAAU;YACV,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;IAC5D;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,MAAM;IAChE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAgB;oCAAG,MAAM,MAAM;oCAAC;oCAAM,uBAAuB,MAAM;oCAAC;;;;;;;;;;;;;kCAEnF,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACxE,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;oBAMjC,6BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAuB,CAAC;wCAC9F,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;;oDAAM;oDAAK,MAAM,MAAM;oDAAC;;;;;;;0DACtC,8OAAC;gDAAO,OAAM;;oDAAU;oDAAM,eAAe;oDAAW;;;;;;;0DACxD,8OAAC;gDAAO,OAAM;;oDAAc;oDAAM,eAAe;oDAAe;;;;;;;0DAChE,8OAAC;gDAAO,OAAM;;oDAAY;oDAAM,eAAe;oDAAa;;;;;;;0DAC5D,8OAAC;gDAAO,OAAM;;oDAAY;oDAAM,eAAe;oDAAa;;;;;;;;;;;;;;;;;;;0CAKhE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,QAAQ,QAAQ;wCACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAyB,CAAC;wCAClG,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;;oDAAM;oDAAK,MAAM,MAAM;oDAAC;;;;;;;0DACtC,8OAAC;gDAAO,OAAM;;oDAAO;oDAAK,iBAAiB;oDAAQ;;;;;;;0DACnD,8OAAC;gDAAO,OAAM;;oDAAc;oDAAO,iBAAiB;oDAAe;;;;;;;0DACnE,8OAAC;gDAAO,OAAM;;oDAAgB;oDAAK,iBAAiB;oDAAiB;;;;;;;;;;;;;;;;;;;0CAKzE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;0DAExB,8OAAC;gDACC,SAAS,IAAM,iBAAiB,kBAAkB,QAAQ,SAAS;gDACnE,WAAU;0DAET,kBAAkB,sBAAQ,8OAAC,8NAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAAe,8OAAC,iOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMvF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP,WAAW;4CAAE,QAAQ;4CAAO,UAAU;4CAAO,QAAQ;wCAAG;wCACxD,UAAU;wCACV,iBAAiB;oCACnB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;2BAElC,uBAAuB,MAAM,KAAK,kBACpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,QAAQ,QAAQ,KAAK,QAChE,gBACA;;;;;;;;;;2BAIR,uBAAuB,GAAG,CAAC,CAAA,qBACzB,8OAAC,uIAAA,CAAA,UAAQ;wBAEP,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,gBAAgB;wBAChB,SAAS;wBACT,YAAY;uBANP,KAAK,EAAE;;;;;;;;;;0BAapB,8OAAC,2IAAA,CAAA,UAAY;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,eAAe;gBACjB;gBACA,QAAQ;;;;;;0BAIV,8OAAC,gJAAA,CAAA,UAAiB;gBAChB,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,gBAAgB;gBAClB;gBACA,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 6403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/tasks/index.ts"], "sourcesContent": ["/**\n * 任务管理组件统一导出\n */\n\nexport { default as TaskList } from './TaskList';\nexport { default as TaskItem } from './TaskItem';\nexport { default as TaskEditForm } from './TaskEditForm';\nexport { default as TaskDeleteConfirm } from './TaskDeleteConfirm';\n"], "names": [], "mappings": "AAAA;;CAEC;AAED;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 6442, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/app/tasks/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useTaskStore } from '@/store/useTaskStore';\nimport { TaskList } from '@/components/tasks';\nimport { Task, TaskStatus } from '@/shared';\n\nexport default function TasksPage() {\n  const router = useRouter();\n  const { user } = useAuthStore();\n  const { \n    tasks, \n    loading, \n    fetchTasks, \n    updateTask, \n    deleteTask, \n    completeTask,\n    startTask,\n    pauseTask\n  } = useTaskStore();\n\n  useEffect(() => {\n    if (!user) {\n      router.push('/auth/signin');\n      return;\n    }\n\n    // 获取任务列表\n    fetchTasks(user.id);\n  }, [user, router, fetchTasks]);\n\n  const handleCreateTask = () => {\n    router.push('/tasks/new');\n  };\n\n  const handleEditTask = async (taskId: string, updates: Partial<Task>) => {\n    try {\n      await updateTask(taskId, updates);\n    } catch (error) {\n      console.error('Failed to update task:', error);\n      throw error;\n    }\n  };\n\n  const handleDeleteTask = async (taskId: string) => {\n    try {\n      await deleteTask(taskId);\n    } catch (error) {\n      console.error('Failed to delete task:', error);\n      throw error;\n    }\n  };\n\n  const handleStatusChange = async (taskId: string, status: TaskStatus) => {\n    try {\n      await updateTask(taskId, { status });\n    } catch (error) {\n      console.error('Failed to update task status:', error);\n      throw error;\n    }\n  };\n\n  const handleStartTask = async (taskId: string) => {\n    try {\n      await startTask(taskId);\n    } catch (error) {\n      console.error('Failed to start task:', error);\n      throw error;\n    }\n  };\n\n  const handleCompleteTask = async (taskId: string) => {\n    try {\n      await completeTask(taskId);\n    } catch (error) {\n      console.error('Failed to complete task:', error);\n      throw error;\n    }\n  };\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => router.push('/dashboard')}\n                className=\"text-gray-500 hover:text-gray-700 mr-4\"\n              >\n                ← 返回仪表板\n              </button>\n              <h1 className=\"text-2xl font-bold text-gray-900\">任务管理</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                欢迎，{user.email}\n              </span>\n              <button\n                onClick={() => router.push('/dashboard')}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                仪表板\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <TaskList\n          tasks={tasks}\n          loading={loading}\n          onCreateTask={handleCreateTask}\n          onEditTask={handleEditTask}\n          onDeleteTask={handleDeleteTask}\n          onStatusChange={handleStatusChange}\n          onStartTask={handleStartTask}\n          onCompleteTask={handleCompleteTask}\n        />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,KAAK,EACL,OAAO,EACP,UAAU,EACV,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACV,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,SAAS;QACT,WAAW,KAAK,EAAE;IACpB,GAAG;QAAC;QAAM;QAAQ;KAAW;IAE7B,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,OAAO,QAAgB;QAC5C,IAAI;YACF,MAAM,WAAW,QAAQ;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,QAAQ;gBAAE;YAAO;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,aAAa;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAClC,KAAK,KAAK;;;;;;;kDAEhB,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,8KAAA,CAAA,WAAQ;oBACP,OAAO;oBACP,SAAS;oBACT,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,gBAAgB;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}]}