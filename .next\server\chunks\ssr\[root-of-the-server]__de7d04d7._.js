module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 应用常量定义
 */ // ============================================================================
// 任务相关常量
// ============================================================================
__turbopack_context__.s({
    "ALGORITHM_CONSTANTS": (()=>ALGORITHM_CONSTANTS),
    "ALL_DAYS": (()=>ALL_DAYS),
    "DAY_NAMES_CN": (()=>DAY_NAMES_CN),
    "DEFAULT_USER_CONFIG": (()=>DEFAULT_USER_CONFIG),
    "ERROR_CODES": (()=>ERROR_CODES),
    "PRIORITY_LEVELS": (()=>PRIORITY_LEVELS),
    "QUADRANTS": (()=>QUADRANTS),
    "TASK_CATEGORIES": (()=>TASK_CATEGORIES),
    "TASK_STATUSES": (()=>TASK_STATUSES),
    "TIME_CONSTANTS": (()=>TIME_CONSTANTS),
    "UI_CONSTANTS": (()=>UI_CONSTANTS),
    "WORK_DAYS": (()=>WORK_DAYS)
});
const TASK_CATEGORIES = {
    WORK: 'work',
    IMPROVEMENT: 'improvement',
    ENTERTAINMENT: 'entertainment'
};
const TASK_STATUSES = {
    PENDING: 'pending',
    IN_PROGRESS: 'in-progress',
    COMPLETED: 'completed',
    POSTPONED: 'postponed'
};
const PRIORITY_LEVELS = {
    VERY_LOW: 1,
    LOW: 2,
    MEDIUM: 3,
    HIGH: 4,
    VERY_HIGH: 5
};
const QUADRANTS = {
    URGENT_IMPORTANT: 1,
    IMPORTANT_NOT_URGENT: 2,
    URGENT_NOT_IMPORTANT: 3,
    NOT_URGENT_NOT_IMPORTANT: 4 // 不紧急不重要
};
const TIME_CONSTANTS = {
    MINUTES_PER_HOUR: 60,
    HOURS_PER_DAY: 24,
    DAYS_PER_WEEK: 7,
    DEFAULT_BREAK_DURATION: 15,
    DEFAULT_TASK_DURATION: 60,
    MIN_TASK_DURATION: 15,
    MAX_TASK_DURATION: 480 // 分钟 (8小时)
};
const WORK_DAYS = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
];
const ALL_DAYS = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday'
];
const DAY_NAMES_CN = {
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
};
const DEFAULT_USER_CONFIG = {
    workStart: '09:00',
    workEnd: '18:00',
    workDays: WORK_DAYS,
    sleepStart: '23:00',
    sleepEnd: '07:00',
    fixedSlots: [
        {
            start: '07:00',
            end: '08:00',
            type: 'personal',
            label: '晨间例行'
        },
        {
            start: '12:00',
            end: '13:00',
            type: 'meal',
            label: '午餐时间'
        },
        {
            start: '18:30',
            end: '19:30',
            type: 'meal',
            label: '晚餐时间'
        }
    ],
    commuteToWork: 30,
    commuteFromWork: 30,
    preferredWorkHours: 'morning',
    maxContinuousWork: 120,
    breakInterval: 25,
    categoryPreferences: {
        work: {
            preferredTimes: [
                '09:00-12:00',
                '14:00-18:00'
            ],
            maxDaily: 480
        },
        improvement: {
            preferredTimes: [
                '07:00-09:00',
                '19:00-21:00'
            ],
            maxDaily: 120
        },
        entertainment: {
            preferredTimes: [
                '20:00-22:00'
            ],
            maxDaily: 180
        }
    }
};
const ALGORITHM_CONSTANTS = {
    // 四象限权重
    QUADRANT_WEIGHTS: {
        [QUADRANTS.URGENT_IMPORTANT]: 1.0,
        [QUADRANTS.IMPORTANT_NOT_URGENT]: 0.8,
        [QUADRANTS.URGENT_NOT_IMPORTANT]: 0.6,
        [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: 0.4
    },
    // 推迟惩罚系数
    POSTPONE_PENALTY: 0.1,
    // 截止日期权重
    DEADLINE_WEIGHT: 0.3,
    // 分类权重
    CATEGORY_WEIGHTS: {
        [TASK_CATEGORIES.WORK]: 1.0,
        [TASK_CATEGORIES.IMPROVEMENT]: 0.8,
        [TASK_CATEGORIES.ENTERTAINMENT]: 0.6
    }
};
const UI_CONSTANTS = {
    // 颜色主题
    COLORS: {
        PRIMARY: '#4F46E5',
        SUCCESS: '#10B981',
        WARNING: '#F59E0B',
        ERROR: '#EF4444',
        INFO: '#3B82F6'
    },
    // 象限颜色
    QUADRANT_COLORS: {
        [QUADRANTS.URGENT_IMPORTANT]: {
            bg: 'bg-red-100',
            border: 'border-red-300',
            text: 'text-red-800',
            selectedBg: 'bg-red-200',
            selectedBorder: 'border-red-500'
        },
        [QUADRANTS.IMPORTANT_NOT_URGENT]: {
            bg: 'bg-blue-100',
            border: 'border-blue-300',
            text: 'text-blue-800',
            selectedBg: 'bg-blue-200',
            selectedBorder: 'border-blue-500'
        },
        [QUADRANTS.URGENT_NOT_IMPORTANT]: {
            bg: 'bg-yellow-100',
            border: 'border-yellow-300',
            text: 'text-yellow-800',
            selectedBg: 'bg-yellow-200',
            selectedBorder: 'border-yellow-500'
        },
        [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: {
            bg: 'bg-gray-100',
            border: 'border-gray-300',
            text: 'text-gray-800',
            selectedBg: 'bg-gray-200',
            selectedBorder: 'border-gray-500'
        }
    },
    // 分页
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
};
const ERROR_CODES = {
    // 通用错误
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    // 认证错误
    UNAUTHORIZED: 'UNAUTHORIZED',
    FORBIDDEN: 'FORBIDDEN',
    // 任务相关错误
    TASK_NOT_FOUND: 'TASK_NOT_FOUND',
    TASK_CREATION_FAILED: 'TASK_CREATION_FAILED',
    TASK_UPDATE_FAILED: 'TASK_UPDATE_FAILED',
    // 用户配置错误
    USER_CONFIG_NOT_FOUND: 'USER_CONFIG_NOT_FOUND',
    USER_CONFIG_INVALID: 'USER_CONFIG_INVALID',
    // 算法错误
    ALGORITHM_EXECUTION_FAILED: 'ALGORITHM_EXECUTION_FAILED',
    SCHEDULE_GENERATION_FAILED: 'SCHEDULE_GENERATION_FAILED'
};
}}),
"[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 日期和时间相关的工具函数
 */ __turbopack_context__.s({
    "createDateTime": (()=>createDateTime),
    "formatDate": (()=>formatDate),
    "formatDateTime": (()=>formatDateTime),
    "formatDuration": (()=>formatDuration),
    "formatTime": (()=>formatTime),
    "getDaysDifference": (()=>getDaysDifference),
    "getDefaultDeadline": (()=>getDefaultDeadline),
    "getEndOfDay": (()=>getEndOfDay),
    "getEndOfWeek": (()=>getEndOfWeek),
    "getMinutesBetween": (()=>getMinutesBetween),
    "getNextWorkday": (()=>getNextWorkday),
    "getRelativeTime": (()=>getRelativeTime),
    "getStartOfDay": (()=>getStartOfDay),
    "getStartOfWeek": (()=>getStartOfWeek),
    "getTomorrow": (()=>getTomorrow),
    "isSameDay": (()=>isSameDay),
    "isTimeInRange": (()=>isTimeInRange),
    "isTimeRangeOverlap": (()=>isTimeRangeOverlap),
    "isToday": (()=>isToday),
    "isTomorrow": (()=>isTomorrow),
    "isWorkday": (()=>isWorkday),
    "parseTimeToday": (()=>parseTimeToday)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
;
function formatDate(date) {
    return date.toISOString().split('T')[0];
}
function formatTime(date) {
    return date.toTimeString().slice(0, 5);
}
function formatDateTime(date) {
    return date.toISOString().slice(0, 16);
}
function formatDuration(minutes) {
    if (minutes < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR) {
        return `${minutes}分钟`;
    }
    const hours = Math.floor(minutes / __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR);
    const remainingMinutes = minutes % __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR;
    if (remainingMinutes === 0) {
        return `${hours}小时`;
    }
    return `${hours}小时${remainingMinutes}分钟`;
}
function getStartOfDay(date = new Date()) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    return start;
}
function getEndOfDay(date = new Date()) {
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return end;
}
function getTomorrow(date = new Date()) {
    const tomorrow = new Date(date);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
}
function getStartOfWeek(date = new Date()) {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    start.setDate(diff);
    return getStartOfDay(start);
}
function getEndOfWeek(date = new Date()) {
    const end = new Date(getStartOfWeek(date));
    end.setDate(end.getDate() + 6);
    return getEndOfDay(end);
}
function isSameDay(date1, date2) {
    return formatDate(date1) === formatDate(date2);
}
function isToday(date) {
    return isSameDay(date, new Date());
}
function isTomorrow(date) {
    return isSameDay(date, getTomorrow());
}
function getDaysDifference(date1, date2) {
    const timeDiff = date2.getTime() - date1.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
}
function parseTimeToday(timeStr, baseDate = new Date()) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date(baseDate);
    date.setHours(hours, minutes, 0, 0);
    return date;
}
function createDateTime(date, timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const result = new Date(date);
    result.setHours(hours, minutes, 0, 0);
    return result;
}
function getDefaultDeadline() {
    const tomorrow = getTomorrow();
    tomorrow.setHours(18, 0, 0, 0);
    return tomorrow;
}
function isTimeInRange(time, startTime, endTime, baseDate = new Date()) {
    const start = parseTimeToday(startTime, baseDate);
    const end = parseTimeToday(endTime, baseDate);
    return time >= start && time <= end;
}
function isTimeRangeOverlap(start1, end1, start2, end2) {
    return start1 < end2 && end1 > start2;
}
function getMinutesBetween(start, end) {
    return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
}
function isWorkday(date, workDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
]) {
    const dayNames = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday'
    ];
    const dayName = dayNames[date.getDay()];
    return workDays.includes(dayName);
}
function getNextWorkday(date = new Date(), workDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
]) {
    let nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    while(!isWorkday(nextDay, workDays)){
        nextDay.setDate(nextDay.getDate() + 1);
    }
    return nextDay;
}
function getRelativeTime(date) {
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays === 0) {
        return '今天';
    } else if (diffDays === 1) {
        return '明天';
    } else if (diffDays === -1) {
        return '昨天';
    } else if (diffDays > 1 && diffDays <= 7) {
        return `${diffDays}天后`;
    } else if (diffDays < -1 && diffDays >= -7) {
        return `${Math.abs(diffDays)}天前`;
    } else {
        return formatDate(date);
    }
}
}}),
"[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务相关的工具函数
 */ __turbopack_context__.s({
    "canStartTask": (()=>canStartTask),
    "classifyQuadrant": (()=>classifyQuadrant),
    "filterTasksByCategory": (()=>filterTasksByCategory),
    "filterTasksByQuadrant": (()=>filterTasksByQuadrant),
    "filterTasksByStatus": (()=>filterTasksByStatus),
    "getCategoryColor": (()=>getCategoryColor),
    "getCategoryIcon": (()=>getCategoryIcon),
    "getCategoryName": (()=>getCategoryName),
    "getPriorityColor": (()=>getPriorityColor),
    "getPriorityName": (()=>getPriorityName),
    "getQuadrantDescription": (()=>getQuadrantDescription),
    "getQuadrantTitle": (()=>getQuadrantTitle),
    "getStatusColor": (()=>getStatusColor),
    "getStatusName": (()=>getStatusName),
    "getTaskUrgencyDescription": (()=>getTaskUrgencyDescription),
    "getTodayTasks": (()=>getTodayTasks),
    "isTaskCompleted": (()=>isTaskCompleted),
    "isTaskDueSoon": (()=>isTaskDueSoon),
    "isTaskInProgress": (()=>isTaskInProgress),
    "isTaskOverdue": (()=>isTaskOverdue),
    "sortTasksByDeadline": (()=>sortTasksByDeadline),
    "sortTasksByPriority": (()=>sortTasksByPriority),
    "validateTask": (()=>validateTask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
;
function classifyQuadrant(importance, urgency) {
    if (importance >= 4 && urgency >= 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT;
    } else if (importance >= 4 && urgency < 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT;
    } else if (importance < 4 && urgency >= 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT;
    } else {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT;
    }
}
function getQuadrantDescription(quadrant) {
    const descriptions = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT]: '重要且紧急 - 立即执行',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT]: '重要不紧急 - 计划执行',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT]: '不重要但紧急 - 委托处理',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT]: '不重要不紧急 - 减少或删除'
    };
    return descriptions[quadrant];
}
function getQuadrantTitle(quadrant) {
    const titles = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT]: '象限 I',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT]: '象限 II',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT]: '象限 III',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT]: '象限 IV'
    };
    return titles[quadrant];
}
function getCategoryName(category) {
    const names = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: '工作',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: '提升',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: '娱乐'
    };
    return names[category];
}
function getCategoryColor(category) {
    const colors = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: 'blue',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: 'green',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: 'purple'
    };
    return colors[category];
}
function getCategoryIcon(category) {
    const icons = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: '💼',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: '📚',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: '🎮'
    };
    return icons[category];
}
function getStatusName(status) {
    const names = {
        pending: '待处理',
        'in-progress': '进行中',
        completed: '已完成',
        postponed: '已推迟'
    };
    return names[status];
}
function getStatusColor(status) {
    const colors = {
        pending: 'gray',
        'in-progress': 'blue',
        completed: 'green',
        postponed: 'yellow'
    };
    return colors[status];
}
function canStartTask(task) {
    return task.status === 'pending' || task.status === 'postponed';
}
function isTaskCompleted(task) {
    return task.status === 'completed';
}
function isTaskInProgress(task) {
    return task.status === 'in-progress';
}
function getPriorityName(priority) {
    const names = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_LOW]: '很低',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].LOW]: '较低',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].MEDIUM]: '中等',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].HIGH]: '较高',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_HIGH]: '很高'
    };
    return names[priority];
}
function getPriorityColor(priority) {
    const colors = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_LOW]: 'gray',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].LOW]: 'blue',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].MEDIUM]: 'yellow',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].HIGH]: 'orange',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_HIGH]: 'red'
    };
    return colors[priority];
}
function filterTasksByCategory(tasks, category) {
    return tasks.filter((task)=>task.category === category);
}
function filterTasksByStatus(tasks, status) {
    return tasks.filter((task)=>task.status === status);
}
function filterTasksByQuadrant(tasks, quadrant) {
    return tasks.filter((task)=>classifyQuadrant(task.importance, task.urgency) === quadrant);
}
function getTodayTasks(tasks) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tasks.filter((task)=>{
        // 包含今日截止的任务和未完成的高优先级任务
        const isToday = task.deadline <= tomorrow;
        const isHighPriority = task.importance >= 4 || task.urgency >= 4;
        const isPending = task.status === 'pending' || task.status === 'in-progress';
        return isPending && (isToday || isHighPriority);
    });
}
function sortTasksByPriority(tasks) {
    return [
        ...tasks
    ].sort((a, b)=>{
        const aQuadrant = classifyQuadrant(a.importance, a.urgency);
        const bQuadrant = classifyQuadrant(b.importance, b.urgency);
        // 先按象限排序
        if (aQuadrant !== bQuadrant) {
            return aQuadrant - bQuadrant;
        }
        // 再按重要性排序
        if (a.importance !== b.importance) {
            return b.importance - a.importance;
        }
        // 最后按紧急性排序
        return b.urgency - a.urgency;
    });
}
function sortTasksByDeadline(tasks) {
    return [
        ...tasks
    ].sort((a, b)=>a.deadline.getTime() - b.deadline.getTime());
}
function validateTask(task) {
    const errors = [];
    if (!task.title || task.title.trim().length === 0) {
        errors.push('任务标题不能为空');
    }
    if (task.title && task.title.length > 100) {
        errors.push('任务标题不能超过100个字符');
    }
    if (!task.category || !Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"]).includes(task.category)) {
        errors.push('请选择有效的任务分类');
    }
    if (!task.importance || task.importance < 1 || task.importance > 5) {
        errors.push('重要性必须在1-5之间');
    }
    if (!task.urgency || task.urgency < 1 || task.urgency > 5) {
        errors.push('紧急性必须在1-5之间');
    }
    if (!task.deadline) {
        errors.push('请设置截止时间');
    } else if (task.deadline < new Date()) {
        errors.push('截止时间不能早于当前时间');
    }
    if (!task.estimatedDuration || task.estimatedDuration < 15 || task.estimatedDuration > 480) {
        errors.push('预估时长必须在15分钟到8小时之间');
    }
    return errors;
}
function isTaskDueSoon(task, hoursThreshold = 24) {
    const now = new Date();
    const timeDiff = task.deadline.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    return hoursDiff > 0 && hoursDiff <= hoursThreshold;
}
function isTaskOverdue(task) {
    return task.deadline < new Date() && task.status !== 'completed';
}
function getTaskUrgencyDescription(task) {
    if (isTaskOverdue(task)) {
        return '已过期';
    } else if (isTaskDueSoon(task, 2)) {
        return '2小时内到期';
    } else if (isTaskDueSoon(task, 24)) {
        return '今日到期';
    } else if (isTaskDueSoon(task, 48)) {
        return '明日到期';
    } else {
        return '充足时间';
    }
}
}}),
"[project]/src/shared/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 共享模块的统一导出
 * 提供类型、常量、工具函数的统一入口
 */ // ============================================================================
// 类型导出
// ============================================================================
__turbopack_context__.s({
    "createAppError": (()=>createAppError),
    "deepClone": (()=>deepClone),
    "hoursToMinutes": (()=>hoursToMinutes),
    "isAppError": (()=>isAppError),
    "isValidPriority": (()=>isValidPriority),
    "isValidQuadrant": (()=>isValidQuadrant),
    "isValidTaskCategory": (()=>isValidTaskCategory),
    "isValidTaskStatus": (()=>isValidTaskStatus),
    "minutesToHours": (()=>minutesToHours),
    "safeJsonParse": (()=>safeJsonParse)
});
// ============================================================================
// 常量导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
// ============================================================================
// 工具函数导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
;
;
;
function isValidTaskCategory(value) {
    const validCategories = [
        'work',
        'improvement',
        'entertainment'
    ];
    return validCategories.includes(value);
}
function isValidPriority(value) {
    return typeof value === 'number' && value >= 1 && value <= 5;
}
function isValidTaskStatus(value) {
    const validStatuses = [
        'pending',
        'in-progress',
        'completed',
        'postponed'
    ];
    return validStatuses.includes(value);
}
function isValidQuadrant(value) {
    return typeof value === 'number' && value >= 1 && value <= 4;
}
function createAppError(code, message, details) {
    return {
        code,
        message,
        details,
        timestamp: new Date()
    };
}
function isAppError(error) {
    return error && typeof error.code === 'string' && typeof error.message === 'string' && error.timestamp instanceof Date;
}
function hoursToMinutes(hours) {
    return Math.round(hours * TIME_CONSTANTS.MINUTES_PER_HOUR);
}
function minutesToHours(minutes) {
    return minutes / TIME_CONSTANTS.MINUTES_PER_HOUR;
}
function safeJsonParse(json, defaultValue) {
    try {
        return JSON.parse(json);
    } catch  {
        return defaultValue;
    }
}
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (Array.isArray(obj)) {
        return obj.map((item)=>deepClone(item));
    }
    const cloned = {};
    for(const key in obj){
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}
}}),
"[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 时间规划算法
 * 负责根据任务优先级和用户时间配置生成智能的时间安排
 */ __turbopack_context__.s({
    "PlanningAlgorithm": (()=>PlanningAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
;
class PlanningAlgorithm {
    /**
   * 生成今日时间安排
   */ generateDailySchedule(tasks, userTimeConfig) {
        // 1. 过滤今日需要处理的任务
        const todayTasks = this.filterTodayTasks(tasks);
        // 2. 计算分数并分类
        const scoredTasks = todayTasks.map((task)=>({
                ...task,
                score: this.calculateTaskScore(task),
                quadrant: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency)
            })).sort((a, b)=>{
            // 先按象限排序，再按分数排序
            if (a.quadrant !== b.quadrant) {
                return a.quadrant - b.quadrant;
            }
            return b.score - a.score;
        });
        // 3. 生成时间段（使用新的基于任务类型的算法）
        const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);
        // 4. 计算总时长
        const totalDuration = timeSlots.reduce((sum, slot)=>sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0);
        return {
            date: new Date(),
            timeSlots,
            totalTasks: todayTasks.length,
            estimatedDuration: totalDuration
        };
    }
    /**
   * 过滤今日需要处理的任务
   */ filterTodayTasks(tasks) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTodayTasks"])(tasks);
    }
    /**
   * 计算任务综合分数
   */ calculateTaskScore(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        // 基础分数：象限权重 * (重要性 + 紧急性)
        const baseScore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].QUADRANT_WEIGHTS[quadrant] * (task.importance + task.urgency);
        // 截止日期权重
        const now = new Date();
        const timeToDeadline = task.deadline.getTime() - now.getTime();
        const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
        let deadlineWeight = 1;
        if (hoursToDeadline < 2) {
            deadlineWeight = 2.0; // 2小时内，权重翻倍
        } else if (hoursToDeadline < 24) {
            deadlineWeight = 1.5; // 24小时内，权重增加50%
        } else if (hoursToDeadline < 48) {
            deadlineWeight = 1.2; // 48小时内，权重增加20%
        }
        // 推迟惩罚
        const postponePenalty = task.postponeCount * __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].POSTPONE_PENALTY;
        // 分类权重
        const categoryWeight = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].CATEGORY_WEIGHTS[task.category];
        return baseScore * deadlineWeight * categoryWeight * (1 + postponePenalty);
    }
    /**
   * 基于任务类型生成时间段安排
   */ generateTimeSlotsWithCategories(tasks, userTimeConfig) {
        const timeSlots = [];
        const today = new Date();
        // 使用默认配置如果没有提供用户配置
        const config = userTimeConfig || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_USER_CONFIG"];
        // 按任务类型分组
        const tasksByCategory = this.groupTasksByCategory(tasks);
        // 为每种类型的任务安排时间
        for (const [category, categoryTasks] of Object.entries(tasksByCategory)){
            if (categoryTasks.length === 0) continue;
            const categoryPrefs = config.categoryPreferences[category];
            if (!categoryPrefs) continue;
            // 为该类型任务生成可用时间段
            const availableSlots = this.generateAvailableSlots(categoryPrefs.preferredTimes, config.fixedSlots, today);
            // 在可用时间段中安排任务
            this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);
        }
        return timeSlots.sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());
    }
    /**
   * 按任务类型分组
   */ groupTasksByCategory(tasks) {
        return {
            work: tasks.filter((task)=>task.category === 'work'),
            improvement: tasks.filter((task)=>task.category === 'improvement'),
            entertainment: tasks.filter((task)=>task.category === 'entertainment')
        };
    }
    /**
   * 生成可用时间段
   */ generateAvailableSlots(preferredTimes, fixedSlots, date) {
        const availableSlots = [];
        for (const timeRange of preferredTimes){
            const [startTime, endTime] = timeRange.split('-');
            const [startHour, startMinute] = startTime.split(':').map(Number);
            const [endHour, endMinute] = endTime.split(':').map(Number);
            const slotStart = new Date(date);
            slotStart.setHours(startHour, startMinute, 0, 0);
            const slotEnd = new Date(date);
            slotEnd.setHours(endHour, endMinute, 0, 0);
            // 检查是否与固定时间段冲突
            let hasConflict = false;
            for (const fixedSlot of fixedSlots){
                const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);
                const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);
                const fixedStart = new Date(date);
                fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);
                const fixedEnd = new Date(date);
                fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);
                // 检查时间段重叠
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTimeRangeOverlap"])(slotStart, slotEnd, fixedStart, fixedEnd)) {
                    hasConflict = true;
                    break;
                }
            }
            if (!hasConflict) {
                availableSlots.push({
                    start: slotStart,
                    end: slotEnd
                });
            }
        }
        return availableSlots;
    }
    /**
   * 在可用时间段中安排任务
   */ scheduleTasksInSlots(tasks, availableSlots, timeSlots) {
        let currentSlotIndex = 0;
        let currentTime = availableSlots[0]?.start;
        if (!currentTime) return;
        for (const task of tasks){
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            // 寻找合适的时间段
            while(currentSlotIndex < availableSlots.length){
                const currentSlot = availableSlots[currentSlotIndex];
                const remainingTime = currentSlot.end.getTime() - currentTime.getTime();
                if (remainingTime >= taskDuration) {
                    // 在当前时间段安排任务
                    const endTime = new Date(currentTime.getTime() + taskDuration);
                    timeSlots.push({
                        task,
                        startTime: new Date(currentTime),
                        endTime,
                        isFixed: false
                    });
                    // 更新当前时间，添加15分钟休息时间
                    currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
                    break;
                } else {
                    // 移动到下一个时间段
                    currentSlotIndex++;
                    currentTime = availableSlots[currentSlotIndex]?.start;
                    if (!currentTime) break;
                }
            }
        }
    }
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT) {
            return '🔥 高优先级任务，建议立即处理';
        } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT) {
            return '📅 重要任务，建议合理安排时间';
        } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT) {
            return '⚡ 紧急但不重要，考虑委托或快速处理';
        } else {
            return '🤔 优先级较低，可以延后或删除';
        }
    }
    /**
   * 生成时间段安排（保留原方法作为后备）
   */ generateTimeSlots(tasks, workHours) {
        const timeSlots = [];
        const today = new Date();
        // 解析工作时间
        const [startHour, startMinute] = workHours.start.split(':').map(Number);
        const [endHour, endMinute] = workHours.end.split(':').map(Number);
        let currentTime = new Date(today);
        currentTime.setHours(startHour, startMinute, 0, 0);
        const workEndTime = new Date(today);
        workEndTime.setHours(endHour, endMinute, 0, 0);
        for (const task of tasks){
            // 检查是否还有足够的工作时间
            const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            if (remainingWorkTime < taskDuration) {
                continue;
            }
            const endTime = new Date(currentTime.getTime() + taskDuration);
            timeSlots.push({
                task,
                startTime: new Date(currentTime),
                endTime,
                isFixed: false
            });
            // 更新当前时间，添加15分钟休息时间
            currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
            // 如果超过工作时间，停止安排
            if (currentTime >= workEndTime) {
                break;
            }
        }
        return timeSlots;
    }
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 生活平衡算法
 * 分析用户的时间分配，提供生活平衡建议
 */ __turbopack_context__.s({
    "BalanceAlgorithm": (()=>BalanceAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
;
class BalanceAlgorithm {
    /**
   * 分析用户的生活平衡状况
   */ async analyzeBalance(userId, date = new Date()) {
        // 获取本周的统计数据
        const weeklyStats = await this.calculateWeeklyStats(userId, date);
        // 计算分类比例
        const categoryRatios = this.calculateCategoryRatios(weeklyStats.totalTime);
        // 计算平衡分数
        const balanceScore = this.calculateBalanceScore(categoryRatios);
        // 生成建议
        const recommendations = this.generateRecommendations(categoryRatios, weeklyStats);
        return {
            userId,
            date,
            categoryRatios,
            weeklyStats,
            recommendations,
            balanceScore
        };
    }
    /**
   * 计算本周统计数据
   */ async calculateWeeklyStats(userId, date) {
        const startOfWeek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStartOfWeek"])(date);
        const endOfWeek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEndOfWeek"])(date);
        // 这里应该从数据库获取数据，暂时使用模拟数据
        const mockStats = {
            work: 2400,
            improvement: 420,
            entertainment: 600 // 10小时
        };
        const totalMinutes = mockStats.work + mockStats.improvement + mockStats.entertainment;
        const averageDaily = {
            work: mockStats.work / 7,
            improvement: mockStats.improvement / 7,
            entertainment: mockStats.entertainment / 7
        };
        // 计算趋势（这里简化处理）
        const trend = this.calculateTrend(mockStats);
        return {
            totalTime: mockStats,
            averageDaily,
            trend
        };
    }
    /**
   * 计算分类比例
   */ calculateCategoryRatios(totalTime) {
        const total = totalTime.work + totalTime.improvement + totalTime.entertainment;
        if (total === 0) {
            return {
                work: 0,
                improvement: 0,
                entertainment: 0
            };
        }
        return {
            work: Math.round(totalTime.work / total * 100) / 100,
            improvement: Math.round(totalTime.improvement / total * 100) / 100,
            entertainment: Math.round(totalTime.entertainment / total * 100) / 100
        };
    }
    /**
   * 计算平衡分数 (0-100)
   */ calculateBalanceScore(ratios) {
        // 理想比例：工作60%，提升25%，娱乐15%
        const idealRatios = {
            work: 0.6,
            improvement: 0.25,
            entertainment: 0.15
        };
        // 计算与理想比例的偏差
        const workDeviation = Math.abs(ratios.work - idealRatios.work);
        const improvementDeviation = Math.abs(ratios.improvement - idealRatios.improvement);
        const entertainmentDeviation = Math.abs(ratios.entertainment - idealRatios.entertainment);
        // 总偏差
        const totalDeviation = workDeviation + improvementDeviation + entertainmentDeviation;
        // 转换为分数 (偏差越小，分数越高)
        const score = Math.max(0, 100 - totalDeviation * 100);
        return Math.round(score);
    }
    /**
   * 计算趋势
   */ calculateTrend(currentStats) {
        // 这里应该比较本周与上周的数据
        // 暂时返回稳定状态
        return 'stable';
    }
    /**
   * 生成平衡建议
   */ generateRecommendations(ratios, weeklyStats) {
        const recommendations = [];
        // 工作时间建议
        if (ratios.work > 0.7) {
            recommendations.push('工作时间占比过高，建议适当减少工作量，增加休息时间');
        } else if (ratios.work < 0.5) {
            recommendations.push('工作时间占比较低，可以考虑提高工作效率或增加工作时间');
        }
        // 提升时间建议
        if (ratios.improvement < 0.15) {
            recommendations.push('个人提升时间不足，建议每天安排至少1-2小时用于学习和成长');
        } else if (ratios.improvement > 0.35) {
            recommendations.push('个人提升时间充足，保持良好的学习习惯');
        }
        // 娱乐时间建议
        if (ratios.entertainment < 0.1) {
            recommendations.push('娱乐时间过少，适当的放松有助于提高工作效率');
        } else if (ratios.entertainment > 0.25) {
            recommendations.push('娱乐时间较多，可以考虑将部分时间用于工作或学习');
        }
        // 平衡性建议
        const balanceScore = this.calculateBalanceScore(ratios);
        if (balanceScore >= 80) {
            recommendations.push('时间分配很均衡，继续保持！');
        } else if (balanceScore >= 60) {
            recommendations.push('时间分配基本合理，可以进行微调优化');
        } else {
            recommendations.push('时间分配需要调整，建议重新规划各类活动的时间比例');
        }
        return recommendations;
    }
    /**
   * 更新今日统计
   */ async updateTodayStats(userId, category, duration) {
        const today = new Date();
        const dateStr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDate"])(today);
        // 这里应该更新数据库中的统计数据
        console.log(`更新统计: 用户${userId}, 日期${dateStr}, 分类${category}, 时长${duration}分钟`);
    // 模拟数据库操作
    // await supabase.from('daily_stats').upsert({
    //   user_id: userId,
    //   date: dateStr,
    //   [category + '_time']: duration
    // });
    }
    /**
   * 获取分类时间建议
   */ getCategoryTimeRecommendation(category, currentRatio) {
        const recommendations = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: {
                low: '工作时间不足，建议增加专注工作的时间',
                normal: '工作时间合理，保持当前节奏',
                high: '工作时间过长，注意劳逸结合'
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: {
                low: '学习时间不足，建议每天安排固定的学习时间',
                normal: '学习时间充足，继续保持学习习惯',
                high: '学习时间很充足，可以考虑实践应用'
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: {
                low: '娱乐时间过少，适当放松有助于身心健康',
                normal: '娱乐时间合适，保持工作生活平衡',
                high: '娱乐时间较多，可以考虑更多有意义的活动'
            }
        };
        let level;
        if (currentRatio < 0.2) {
            level = 'low';
        } else if (currentRatio > 0.6) {
            level = 'high';
        } else {
            level = 'normal';
        }
        return recommendations[category][level];
    }
    /**
   * 预测下周建议
   */ predictNextWeekRecommendations(currentStats) {
        const recommendations = [];
        // 基于当前趋势预测
        if (currentStats.trend === 'improving') {
            recommendations.push('本周平衡状况在改善，继续保持当前的时间安排');
        } else if (currentStats.trend === 'declining') {
            recommendations.push('本周平衡状况在下降，建议调整时间分配策略');
        } else {
            recommendations.push('本周时间分配稳定，可以尝试优化某些细节');
        }
        // 基于平均时间给出建议
        const { averageDaily } = currentStats;
        const total = averageDaily.work + averageDaily.improvement + averageDaily.entertainment;
        if (total < 480) {
            recommendations.push('每日活跃时间较少，建议增加有意义的活动');
        } else if (total > 720) {
            recommendations.push('每日活跃时间较长，注意适当休息');
        }
        return recommendations;
    }
    /**
   * 获取平衡分数等级描述
   */ getBalanceScoreDescription(score) {
        if (score >= 90) {
            return '优秀 - 时间分配非常均衡';
        } else if (score >= 80) {
            return '良好 - 时间分配基本均衡';
        } else if (score >= 70) {
            return '一般 - 时间分配需要小幅调整';
        } else if (score >= 60) {
            return '待改善 - 时间分配需要调整';
        } else {
            return '需要改进 - 时间分配严重失衡';
        }
    }
    /**
   * 计算理想的时间分配建议
   */ calculateIdealTimeAllocation(availableHours) {
        const totalMinutes = availableHours * 60;
        return {
            work: Math.round(totalMinutes * 0.6),
            improvement: Math.round(totalMinutes * 0.25),
            entertainment: Math.round(totalMinutes * 0.15) // 15%
        };
    }
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 修复算法
 * 检测和处理推迟的任务，提供修复建议
 */ __turbopack_context__.s({
    "FixAlgorithm": (()=>FixAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
;
class FixAlgorithm {
    /**
   * 分析推迟的任务
   */ analyzePostponedTasks(tasks) {
        const postponedTasks = tasks.filter((task)=>task.postponeCount > 0 || task.status === 'postponed' || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task));
        return postponedTasks.map((task)=>this.createPostponedAlert(task));
    }
    /**
   * 创建推迟任务提醒
   */ createPostponedAlert(task) {
        const daysSinceCreated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDaysDifference"])(task.createdAt, new Date());
        const severity = this.calculateSeverity(task);
        const recommendation = this.generateRecommendation(task);
        return {
            taskId: task.id,
            task,
            postponeCount: task.postponeCount,
            lastPostponeDate: task.updatedAt,
            severity,
            recommendation
        };
    }
    /**
   * 计算严重程度
   */ calculateSeverity(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const postponeCount = task.postponeCount;
        // 已过期的任务
        if (isOverdue) {
            if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT) {
                return 'high';
            } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT || quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT) {
                return 'medium';
            } else {
                return 'low';
            }
        }
        // 根据推迟次数判断
        if (postponeCount >= 3) {
            return quadrant <= 2 ? 'high' : 'medium';
        } else if (postponeCount >= 2) {
            return quadrant === 1 ? 'high' : 'medium';
        } else {
            return quadrant === 1 ? 'medium' : 'low';
        }
    }
    /**
   * 生成修复建议
   */ generateRecommendation(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const postponeCount = task.postponeCount;
        if (isOverdue) {
            return this.getOverdueRecommendation(task, quadrant);
        }
        if (postponeCount >= 3) {
            return this.getHighPostponeRecommendation(task, quadrant);
        }
        if (postponeCount >= 1) {
            return this.getPostponeRecommendation(task, quadrant);
        }
        return '建议尽快处理此任务';
    }
    /**
   * 获取过期任务建议
   */ getOverdueRecommendation(task, quadrant) {
        const daysPastDeadline = Math.abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDaysDifference"])(task.deadline, new Date()));
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return `🚨 任务已过期${daysPastDeadline}天，需要立即处理！考虑重新评估截止时间或寻求帮助。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return `⚠️ 重要任务已过期${daysPastDeadline}天，建议重新安排优先级，尽快完成。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return `⏰ 紧急任务已过期${daysPastDeadline}天，考虑委托他人处理或重新评估必要性。`;
            default:
                return `📅 任务已过期${daysPastDeadline}天，建议评估是否仍需完成，或者删除此任务。`;
        }
    }
    /**
   * 获取高频推迟任务建议
   */ getHighPostponeRecommendation(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return `🔥 此任务已推迟${task.postponeCount}次，建议分解为更小的子任务，或寻求帮助完成。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return `📋 重要任务推迟${task.postponeCount}次，建议设定固定时间块专门处理，避免再次推迟。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return `🤝 紧急任务推迟${task.postponeCount}次，强烈建议委托他人处理或使用自动化工具。`;
            default:
                return `🤔 任务推迟${task.postponeCount}次，建议重新评估其必要性，考虑删除或降低优先级。`;
        }
    }
    /**
   * 获取一般推迟任务建议
   */ getPostponeRecommendation(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return '🎯 高优先级任务，建议立即安排时间处理，避免进一步推迟。';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return '📅 重要任务，建议在日程中安排固定时间，确保按时完成。';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return '⚡ 考虑委托他人处理，或寻找更高效的解决方案。';
            default:
                return '💭 评估任务的实际价值，考虑是否需要继续保留。';
        }
    }
    /**
   * 获取推迟任务的统计信息
   */ getPostponedTasksStats(tasks) {
        const postponedTasks = tasks.filter((task)=>task.postponeCount > 0 || task.status === 'postponed');
        const byCategory = postponedTasks.reduce((acc, task)=>{
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});
        const alerts = this.analyzePostponedTasks(postponedTasks);
        const bySeverity = alerts.reduce((acc, alert)=>{
            acc[alert.severity] = (acc[alert.severity] || 0) + 1;
            return acc;
        }, {});
        const totalPostponeCount = postponedTasks.reduce((sum, task)=>sum + task.postponeCount, 0);
        const averagePostponeCount = postponedTasks.length > 0 ? Math.round(totalPostponeCount / postponedTasks.length * 10) / 10 : 0;
        return {
            totalPostponed: postponedTasks.length,
            byCategory,
            bySeverity,
            averagePostponeCount
        };
    }
    /**
   * 建议任务重新安排策略
   */ suggestRescheduleStrategy(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const postponeCount = task.postponeCount;
        // 高频推迟的任务需要特殊处理
        if (postponeCount >= 3) {
            return this.getHighPostponeStrategy(task, quadrant);
        }
        // 一般推迟任务的策略
        return this.getGeneralStrategy(task, quadrant);
    }
    /**
   * 获取高频推迟任务的策略
   */ getHighPostponeStrategy(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return {
                    strategy: 'immediate_action',
                    breakDown: [
                        '将任务分解为15-30分钟的小块',
                        '立即开始第一个小任务',
                        '寻求同事或朋友的帮助',
                        '移除所有干扰因素'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                const newDeadline = new Date();
                newDeadline.setDate(newDeadline.getDate() + 7);
                return {
                    strategy: 'scheduled_focus',
                    newDeadline,
                    breakDown: [
                        '设定每日固定时间处理',
                        '使用番茄工作法',
                        '设置进度检查点',
                        '奖励机制激励完成'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return {
                    strategy: 'delegate_or_automate',
                    delegation: '寻找可以委托的人员或自动化工具',
                    breakDown: [
                        '评估委托的可能性',
                        '寻找自动化解决方案',
                        '如无法委托，快速批量处理'
                    ]
                };
            default:
                return {
                    strategy: 'eliminate_or_defer',
                    breakDown: [
                        '重新评估任务的必要性',
                        '考虑完全删除此任务',
                        '如必须保留，设定更宽松的时间线'
                    ]
                };
        }
    }
    /**
   * 获取一般任务的重新安排策略
   */ getGeneralStrategy(task, quadrant) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return {
                    strategy: 'priority_scheduling',
                    newDeadline: tomorrow,
                    breakDown: [
                        '安排在最佳工作时间',
                        '清除其他干扰',
                        '专注完成'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                const nextWeek = new Date();
                nextWeek.setDate(nextWeek.getDate() + 7);
                return {
                    strategy: 'planned_execution',
                    newDeadline: nextWeek,
                    breakDown: [
                        '制定详细计划',
                        '分阶段执行',
                        '定期检查进度'
                    ]
                };
            default:
                return {
                    strategy: 'flexible_scheduling',
                    breakDown: [
                        '安排在空闲时间',
                        '与其他类似任务批量处理'
                    ]
                };
        }
    }
    /**
   * 检查任务是否需要紧急关注
   */ needsUrgentAttention(task) {
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const highPostponeCount = task.postponeCount >= 3;
        return isOverdue || quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT && task.postponeCount >= 1 || highPostponeCount && quadrant <= 2;
    }
    /**
   * 生成修复行动计划
   */ generateActionPlan(alerts) {
        const actionPlan = [];
        const highSeverityTasks = alerts.filter((alert)=>alert.severity === 'high');
        const mediumSeverityTasks = alerts.filter((alert)=>alert.severity === 'medium');
        if (highSeverityTasks.length > 0) {
            actionPlan.push(`🚨 立即处理 ${highSeverityTasks.length} 个高优先级推迟任务`);
        }
        if (mediumSeverityTasks.length > 0) {
            actionPlan.push(`⚠️ 本周内处理 ${mediumSeverityTasks.length} 个中等优先级推迟任务`);
        }
        if (alerts.length > 5) {
            actionPlan.push('📋 考虑重新评估任务管理策略，避免过多任务推迟');
        }
        return actionPlan;
    }
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 时间调整算法
 * 当任务超时时，动态调整后续任务的时间安排
 */ __turbopack_context__.s({
    "TimeAdjustmentAlgorithm": (()=>TimeAdjustmentAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
;
class TimeAdjustmentAlgorithm {
    /**
   * 当任务超时时调整后续安排
   */ adjustForOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        const originalDuration = overrunTask.estimatedDuration;
        const overrunMinutes = actualDuration - originalDuration;
        if (overrunMinutes <= 0) {
            return {
                success: true,
                adjustedSchedule: currentSchedule,
                affectedTasks: [],
                message: '任务按时完成，无需调整',
                impactScore: 0
            };
        }
        // 找到超时任务在当前安排中的位置
        const overrunSlotIndex = currentSchedule.findIndex((slot)=>slot.task.id === overrunTask.id);
        if (overrunSlotIndex === -1) {
            return {
                success: false,
                adjustedSchedule: currentSchedule,
                affectedTasks: [],
                message: '未找到超时任务的时间安排',
                impactScore: 0
            };
        }
        // 计算调整策略
        const adjustmentStrategy = this.calculateAdjustmentStrategy(overrunMinutes, currentSchedule, overrunSlotIndex, userTimeConfig);
        return this.applyAdjustmentStrategy(adjustmentStrategy, currentSchedule, overrunSlotIndex, overrunMinutes);
    }
    /**
   * 计算调整策略
   */ calculateAdjustmentStrategy(overrunMinutes, schedule, overrunIndex, userTimeConfig) {
        const remainingSlots = schedule.slice(overrunIndex + 1);
        if (remainingSlots.length === 0) {
            return 'postpone';
        }
        // 计算可压缩的时间
        const compressibleTime = this.calculateCompressibleTime(remainingSlots);
        if (compressibleTime >= overrunMinutes) {
            return 'compress';
        } else if (compressibleTime >= overrunMinutes * 0.6) {
            return 'hybrid'; // 部分压缩，部分推迟
        } else {
            return 'reschedule';
        }
    }
    /**
   * 计算可压缩的时间
   */ calculateCompressibleTime(slots) {
        let compressibleTime = 0;
        for (const slot of slots){
            const task = slot.task;
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            // 根据任务重要性确定可压缩比例
            let compressRatio = 0;
            switch(quadrant){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                    compressRatio = 0.1; // 最多压缩10%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                    compressRatio = 0.2; // 最多压缩20%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                    compressRatio = 0.3; // 最多压缩30%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT:
                    compressRatio = 0.5; // 最多压缩50%
                    break;
            }
            compressibleTime += duration * compressRatio;
        }
        return Math.floor(compressibleTime);
    }
    /**
   * 应用调整策略
   */ applyAdjustmentStrategy(strategy, schedule, overrunIndex, overrunMinutes) {
        const adjustedSchedule = [
            ...schedule
        ];
        const affectedTasks = [];
        let impactScore = 0;
        switch(strategy){
            case 'compress':
                return this.applyCompressionStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'postpone':
                return this.applyPostponeStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'reschedule':
                return this.applyRescheduleStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'hybrid':
                return this.applyHybridStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            default:
                return {
                    success: false,
                    adjustedSchedule: schedule,
                    affectedTasks: [],
                    message: '未知的调整策略',
                    impactScore: 0
                };
        }
    }
    /**
   * 应用压缩策略
   */ applyCompressionStrategy(schedule, overrunIndex, overrunMinutes) {
        const affectedTasks = [];
        let remainingOverrun = overrunMinutes;
        let impactScore = 0;
        // 延长超时任务的结束时间
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 压缩后续任务
        for(let i = overrunIndex + 1; i < schedule.length && remainingOverrun > 0; i++){
            const slot = schedule[i];
            const task = slot.task;
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            // 计算压缩比例
            let compressRatio = 0;
            switch(quadrant){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                    compressRatio = 0.1;
                    impactScore += 10;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                    compressRatio = 0.2;
                    impactScore += 6;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                    compressRatio = 0.3;
                    impactScore += 4;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT:
                    compressRatio = 0.5;
                    impactScore += 2;
                    break;
            }
            const maxCompress = Math.floor(duration * compressRatio);
            const actualCompress = Math.min(maxCompress, remainingOverrun);
            if (actualCompress > 0) {
                // 调整开始时间（向后推迟）
                slot.startTime = new Date(slot.startTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000);
                // 调整结束时间（压缩持续时间）
                slot.endTime = new Date(slot.endTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000 - actualCompress * 60 * 1000);
                affectedTasks.push(task);
                remainingOverrun -= actualCompress;
            } else {
                // 只是向后推迟，不压缩
                slot.startTime = new Date(slot.startTime.getTime() + remainingOverrun * 60 * 1000);
                slot.endTime = new Date(slot.endTime.getTime() + remainingOverrun * 60 * 1000);
            }
        }
        return {
            success: remainingOverrun === 0,
            adjustedSchedule: schedule,
            affectedTasks,
            message: remainingOverrun === 0 ? `成功通过压缩后续任务调整了${overrunMinutes}分钟的超时` : `部分调整成功，仍有${remainingOverrun}分钟需要其他处理`,
            impactScore
        };
    }
    /**
   * 应用推迟策略
   */ applyPostponeStrategy(schedule, overrunIndex, overrunMinutes) {
        // 延长超时任务的结束时间
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 将后续所有任务向后推迟
        for(let i = overrunIndex + 1; i < schedule.length; i++){
            schedule[i].startTime = new Date(schedule[i].startTime.getTime() + overrunMinutes * 60 * 1000);
            schedule[i].endTime = new Date(schedule[i].endTime.getTime() + overrunMinutes * 60 * 1000);
        }
        const affectedTasks = schedule.slice(overrunIndex + 1).map((slot)=>slot.task);
        return {
            success: true,
            adjustedSchedule: schedule,
            affectedTasks,
            message: `所有后续任务向后推迟${overrunMinutes}分钟`,
            impactScore: affectedTasks.length * 3 // 推迟的影响相对较小
        };
    }
    /**
   * 应用重新安排策略
   */ applyRescheduleStrategy(schedule, overrunIndex, overrunMinutes) {
        const affectedTasks = [];
        const remainingSlots = schedule.slice(overrunIndex + 1);
        // 延长超时任务
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 选择低优先级任务移到明天
        const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
        // 移除被重新安排的任务
        const newSchedule = schedule.filter((slot)=>!tasksToReschedule.some((task)=>task.id === slot.task.id));
        // 调整剩余任务的时间
        let timeOffset = overrunMinutes;
        for(let i = overrunIndex + 1; i < newSchedule.length; i++){
            newSchedule[i].startTime = new Date(newSchedule[i].startTime.getTime() + timeOffset * 60 * 1000);
            newSchedule[i].endTime = new Date(newSchedule[i].endTime.getTime() + timeOffset * 60 * 1000);
        }
        return {
            success: true,
            adjustedSchedule: newSchedule,
            affectedTasks: tasksToReschedule,
            message: `${tasksToReschedule.length}个低优先级任务被重新安排到明天`,
            impactScore: tasksToReschedule.length * 8 // 重新安排的影响较大
        };
    }
    /**
   * 应用混合策略
   */ applyHybridStrategy(schedule, overrunIndex, overrunMinutes) {
        // 先尝试压缩
        const compressResult = this.applyCompressionStrategy([
            ...schedule
        ], overrunIndex, overrunMinutes);
        if (compressResult.success) {
            return compressResult;
        }
        // 如果压缩不够，再结合重新安排
        const remainingOverrun = overrunMinutes - this.calculateCompressibleTime(schedule.slice(overrunIndex + 1));
        const rescheduleResult = this.applyRescheduleStrategy([
            ...schedule
        ], overrunIndex, remainingOverrun);
        return {
            success: true,
            adjustedSchedule: rescheduleResult.adjustedSchedule,
            affectedTasks: [
                ...compressResult.affectedTasks,
                ...rescheduleResult.affectedTasks
            ],
            message: `采用混合策略：压缩部分任务，重新安排${rescheduleResult.affectedTasks.length}个任务`,
            impactScore: compressResult.impactScore + rescheduleResult.impactScore
        };
    }
    /**
   * 选择需要重新安排的任务
   */ selectTasksToReschedule(slots, targetMinutes) {
        // 按优先级排序，选择低优先级任务
        const sortedSlots = [
            ...slots
        ].sort((a, b)=>{
            const aQuadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(a.task.importance, a.task.urgency);
            const bQuadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(b.task.importance, b.task.urgency);
            return bQuadrant - aQuadrant; // 降序，低优先级在前
        });
        const tasksToReschedule = [];
        let freedTime = 0;
        for (const slot of sortedSlots){
            if (freedTime >= targetMinutes) break;
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            tasksToReschedule.push(slot.task);
            freedTime += duration;
        }
        return tasksToReschedule;
    }
    /**
   * 预测调整的影响
   */ predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex) {
        const remainingSlots = schedule.slice(overrunIndex + 1);
        const compressibleTime = this.calculateCompressibleTime(remainingSlots);
        if (compressibleTime >= overrunMinutes) {
            return {
                strategy: 'compress',
                affectedTasksCount: remainingSlots.length,
                impactScore: remainingSlots.length * 3,
                description: '通过压缩后续任务时间来调整'
            };
        } else if (remainingSlots.length === 0) {
            return {
                strategy: 'postpone',
                affectedTasksCount: 0,
                impactScore: 0,
                description: '无后续任务，无需调整'
            };
        } else {
            const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
            return {
                strategy: 'reschedule',
                affectedTasksCount: tasksToReschedule.length,
                impactScore: tasksToReschedule.length * 8,
                description: `${tasksToReschedule.length}个任务将被重新安排`
            };
        }
    }
}
}}),
"[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 算法协调器
 * 统一管理和协调所有智能规划算法
 */ __turbopack_context__.s({
    "AlgorithmCoordinator": (()=>AlgorithmCoordinator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-ssr] (ecmascript)");
;
;
;
;
class AlgorithmCoordinator {
    planningAlgorithm;
    balanceAlgorithm;
    fixAlgorithm;
    timeAdjustmentAlgorithm;
    constructor(){
        this.planningAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PlanningAlgorithm"]();
        this.balanceAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BalanceAlgorithm"]();
        this.fixAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FixAlgorithm"]();
        this.timeAdjustmentAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TimeAdjustmentAlgorithm"]();
    }
    // ============================================================================
    // 时间规划相关方法
    // ============================================================================
    /**
   * 生成智能的每日时间安排
   */ async generateDailySchedule(tasks, userTimeConfig) {
        try {
            // 1. 检查推迟任务并给出警告
            const postponedAlerts = this.fixAlgorithm.analyzePostponedTasks(tasks);
            const urgentAlerts = postponedAlerts.filter((alert)=>this.fixAlgorithm.needsUrgentAttention(alert.task));
            // 2. 生成基础时间安排
            const schedule = this.planningAlgorithm.generateDailySchedule(tasks, userTimeConfig);
            // 3. 如果有紧急推迟任务，调整安排优先级
            if (urgentAlerts.length > 0) {
                console.log(`检测到 ${urgentAlerts.length} 个需要紧急处理的推迟任务`);
            // 这里可以进一步优化安排逻辑
            }
            return schedule;
        } catch (error) {
            console.error('生成每日安排时出错:', error);
            throw new Error('无法生成每日时间安排');
        }
    }
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        return this.planningAlgorithm.getTaskRecommendation(task);
    }
    // ============================================================================
    // 生活平衡分析相关方法
    // ============================================================================
    /**
   * 分析用户的生活平衡状况
   */ async analyzeLifeBalance(userId, date) {
        try {
            return await this.balanceAlgorithm.analyzeBalance(userId, date);
        } catch (error) {
            console.error('分析生活平衡时出错:', error);
            throw new Error('无法分析生活平衡状况');
        }
    }
    /**
   * 更新今日统计数据
   */ async updateTodayStats(userId, category, duration) {
        try {
            await this.balanceAlgorithm.updateTodayStats(userId, category, duration);
        } catch (error) {
            console.error('更新统计数据时出错:', error);
            throw new Error('无法更新统计数据');
        }
    }
    /**
   * 获取分类时间建议
   */ getCategoryTimeRecommendation(category, currentRatio) {
        return this.balanceAlgorithm.getCategoryTimeRecommendation(category, currentRatio);
    }
    // ============================================================================
    // 任务修复相关方法
    // ============================================================================
    /**
   * 分析推迟的任务
   */ analyzePostponedTasks(tasks) {
        return this.fixAlgorithm.analyzePostponedTasks(tasks);
    }
    /**
   * 获取推迟任务统计
   */ getPostponedTasksStats(tasks) {
        return this.fixAlgorithm.getPostponedTasksStats(tasks);
    }
    /**
   * 建议任务重新安排策略
   */ suggestRescheduleStrategy(task) {
        return this.fixAlgorithm.suggestRescheduleStrategy(task);
    }
    /**
   * 生成修复行动计划
   */ generateActionPlan(alerts) {
        return this.fixAlgorithm.generateActionPlan(alerts);
    }
    // ============================================================================
    // 时间调整相关方法
    // ============================================================================
    /**
   * 处理任务超时调整
   */ async handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        try {
            const result = this.timeAdjustmentAlgorithm.adjustForOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig);
            // 如果调整成功，更新统计数据
            if (result.success) {
                await this.balanceAlgorithm.updateTodayStats(overrunTask.userId, overrunTask.category, actualDuration);
            }
            return result;
        } catch (error) {
            console.error('处理任务超时时出错:', error);
            throw new Error('无法处理任务超时调整');
        }
    }
    /**
   * 预测调整影响
   */ predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex) {
        return this.timeAdjustmentAlgorithm.predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex);
    }
    // ============================================================================
    // 综合分析和建议
    // ============================================================================
    /**
   * 生成综合的每日建议
   */ async generateDailyInsights(userId, tasks, userTimeConfig) {
        try {
            // 并行执行多个分析
            const [schedule, balanceAnalysis, postponedAlerts] = await Promise.all([
                this.generateDailySchedule(tasks, userTimeConfig),
                this.analyzeLifeBalance(userId),
                Promise.resolve(this.analyzePostponedTasks(tasks))
            ]);
            // 生成综合建议
            const recommendations = this.generateComprehensiveRecommendations(schedule, balanceAnalysis, postponedAlerts);
            return {
                schedule,
                balanceAnalysis,
                postponedAlerts,
                recommendations
            };
        } catch (error) {
            console.error('生成每日洞察时出错:', error);
            throw new Error('无法生成每日洞察');
        }
    }
    /**
   * 生成综合建议
   */ generateComprehensiveRecommendations(schedule, balanceAnalysis, postponedAlerts) {
        const recommendations = [];
        // 基于时间安排的建议
        if (schedule.timeSlots.length === 0) {
            recommendations.push('📅 今日暂无安排的任务，可以处理一些推迟的任务或进行个人提升');
        } else if (schedule.estimatedDuration > 480) {
            recommendations.push('⏰ 今日安排较满，注意劳逸结合，适当休息');
        }
        // 基于生活平衡的建议
        if (balanceAnalysis.balanceScore < 60) {
            recommendations.push('⚖️ 生活平衡需要调整，' + balanceAnalysis.recommendations[0]);
        }
        // 基于推迟任务的建议
        const urgentAlerts = postponedAlerts.filter((alert)=>alert.severity === 'high');
        if (urgentAlerts.length > 0) {
            recommendations.push(`🚨 有 ${urgentAlerts.length} 个高优先级推迟任务需要立即处理`);
        }
        // 基于任务分布的建议
        const categoryDistribution = this.analyzeCategoryDistribution(schedule);
        if (categoryDistribution.work > 0.8) {
            recommendations.push('💼 今日工作任务较多，记得安排适当的休息时间');
        } else if (categoryDistribution.entertainment > 0.4) {
            recommendations.push('🎮 娱乐时间较多，可以考虑增加一些学习或工作任务');
        }
        return recommendations;
    }
    /**
   * 分析任务分类分布
   */ analyzeCategoryDistribution(schedule) {
        const total = schedule.estimatedDuration;
        if (total === 0) return {
            work: 0,
            improvement: 0,
            entertainment: 0
        };
        const distribution = {
            work: 0,
            improvement: 0,
            entertainment: 0
        };
        for (const slot of schedule.timeSlots){
            const duration = (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60);
            distribution[slot.task.category] += duration;
        }
        return {
            work: distribution.work / total,
            improvement: distribution.improvement / total,
            entertainment: distribution.entertainment / total
        };
    }
    // ============================================================================
    // 算法性能监控
    // ============================================================================
    /**
   * 获取算法性能统计
   */ getAlgorithmStats() {
        // 这里可以添加性能监控逻辑
        return {
            planningCalls: 0,
            balanceCalls: 0,
            fixCalls: 0,
            adjustmentCalls: 0
        };
    }
    /**
   * 重置算法统计
   */ resetAlgorithmStats() {
        // 重置性能统计
        console.log('算法统计已重置');
    }
}
}}),
"[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 智能规划服务
 * 提供高级的规划服务接口，封装算法复杂性
 */ __turbopack_context__.s({
    "PlanningService": (()=>PlanningService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-ssr] (ecmascript)");
;
class PlanningService {
    algorithmCoordinator;
    constructor(){
        this.algorithmCoordinator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlgorithmCoordinator"]();
    }
    // ============================================================================
    // 每日规划服务
    // ============================================================================
    /**
   * 生成智能每日规划
   */ async generateDailyPlan(userId, tasks, userTimeConfig) {
        try {
            // 验证输入
            if (!userId) {
                return {
                    success: false,
                    error: '用户ID不能为空',
                    data: null
                };
            }
            if (!Array.isArray(tasks)) {
                return {
                    success: false,
                    error: '任务列表格式不正确',
                    data: null
                };
            }
            // 生成每日洞察
            const insights = await this.algorithmCoordinator.generateDailyInsights(userId, tasks, userTimeConfig);
            return {
                success: true,
                data: {
                    schedule: insights.schedule,
                    insights: {
                        balanceAnalysis: insights.balanceAnalysis,
                        postponedAlerts: insights.postponedAlerts,
                        stats: this.algorithmCoordinator.getPostponedTasksStats(tasks)
                    },
                    recommendations: insights.recommendations
                }
            };
        } catch (error) {
            console.error('生成每日规划失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '生成每日规划时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 快速生成时间安排（简化版）
   */ async generateQuickSchedule(tasks, workHours) {
        try {
            const schedule = this.algorithmCoordinator.planningAlgorithm.generateTimeSlots(tasks.map((task)=>({
                    ...task,
                    score: 0,
                    quadrant: 1
                })), workHours || {
                start: '09:00',
                end: '18:00'
            });
            return {
                success: true,
                data: {
                    date: new Date(),
                    timeSlots: schedule,
                    totalTasks: tasks.length,
                    estimatedDuration: schedule.reduce((sum, slot)=>sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0)
                }
            };
        } catch (error) {
            console.error('生成快速安排失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '生成快速安排时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 生活平衡服务
    // ============================================================================
    /**
   * 获取生活平衡分析
   */ async getBalanceAnalysis(userId) {
        try {
            if (!userId) {
                return {
                    success: false,
                    error: '用户ID不能为空',
                    data: null
                };
            }
            const analysis = await this.algorithmCoordinator.analyzeLifeBalance(userId);
            return {
                success: true,
                data: analysis
            };
        } catch (error) {
            console.error('获取生活平衡分析失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取生活平衡分析时发生错误',
                data: null
            };
        }
    }
    /**
   * 更新活动统计
   */ async updateActivityStats(userId, category, duration) {
        try {
            if (!userId || !category || duration <= 0) {
                return {
                    success: false,
                    error: '参数不完整或无效',
                    data: undefined
                };
            }
            await this.algorithmCoordinator.updateTodayStats(userId, category, duration);
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            console.error('更新活动统计失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '更新活动统计时发生错误',
                data: undefined
            };
        }
    }
    // ============================================================================
    // 任务修复服务
    // ============================================================================
    /**
   * 获取推迟任务分析
   */ async getPostponedTasksAnalysis(tasks) {
        try {
            if (!Array.isArray(tasks)) {
                return {
                    success: false,
                    error: '任务列表格式不正确',
                    data: null
                };
            }
            const alerts = this.algorithmCoordinator.analyzePostponedTasks(tasks);
            const stats = this.algorithmCoordinator.getPostponedTasksStats(tasks);
            const actionPlan = this.algorithmCoordinator.generateActionPlan(alerts);
            return {
                success: true,
                data: {
                    alerts,
                    stats,
                    actionPlan
                }
            };
        } catch (error) {
            console.error('获取推迟任务分析失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取推迟任务分析时发生错误',
                data: null
            };
        }
    }
    /**
   * 获取任务重新安排建议
   */ async getTaskRescheduleAdvice(task) {
        try {
            if (!task || !task.id) {
                return {
                    success: false,
                    error: '任务信息不完整',
                    data: null
                };
            }
            const advice = this.algorithmCoordinator.suggestRescheduleStrategy(task);
            return {
                success: true,
                data: advice
            };
        } catch (error) {
            console.error('获取重新安排建议失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取重新安排建议时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 时间调整服务
    // ============================================================================
    /**
   * 处理任务超时
   */ async handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        try {
            // 验证输入
            if (!overrunTask || !overrunTask.id) {
                return {
                    success: false,
                    error: '超时任务信息不完整',
                    data: null
                };
            }
            if (actualDuration <= 0) {
                return {
                    success: false,
                    error: '实际持续时间必须大于0',
                    data: null
                };
            }
            if (!Array.isArray(currentSchedule)) {
                return {
                    success: false,
                    error: '当前安排格式不正确',
                    data: null
                };
            }
            const result = await this.algorithmCoordinator.handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig);
            return {
                success: true,
                data: result
            };
        } catch (error) {
            console.error('处理任务超时失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '处理任务超时时发生错误',
                data: null
            };
        }
    }
    /**
   * 预测时间调整影响
   */ async predictOverrunImpact(overrunMinutes, schedule, overrunTaskIndex) {
        try {
            if (overrunMinutes <= 0) {
                return {
                    success: false,
                    error: '超时分钟数必须大于0',
                    data: null
                };
            }
            if (!Array.isArray(schedule) || overrunTaskIndex < 0 || overrunTaskIndex >= schedule.length) {
                return {
                    success: false,
                    error: '安排信息或任务索引无效',
                    data: null
                };
            }
            const impact = this.algorithmCoordinator.predictAdjustmentImpact(overrunMinutes, schedule, overrunTaskIndex);
            return {
                success: true,
                data: impact
            };
        } catch (error) {
            console.error('预测调整影响失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '预测调整影响时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 工具方法
    // ============================================================================
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        try {
            return this.algorithmCoordinator.getTaskRecommendation(task);
        } catch (error) {
            console.error('获取任务建议失败:', error);
            return '暂无建议';
        }
    }
    /**
   * 获取分类时间建议
   */ getCategoryAdvice(category, ratio) {
        try {
            return this.algorithmCoordinator.getCategoryTimeRecommendation(category, ratio);
        } catch (error) {
            console.error('获取分类建议失败:', error);
            return '暂无建议';
        }
    }
    /**
   * 获取服务状态
   */ getServiceStatus() {
        try {
            return {
                isHealthy: true,
                algorithmStats: this.algorithmCoordinator.getAlgorithmStats(),
                lastUpdate: new Date()
            };
        } catch (error) {
            console.error('获取服务状态失败:', error);
            return {
                isHealthy: false,
                algorithmStats: null,
                lastUpdate: new Date()
            };
        }
    }
    /**
   * 重置服务统计
   */ resetServiceStats() {
        try {
            this.algorithmCoordinator.resetAlgorithmStats();
        } catch (error) {
            console.error('重置服务统计失败:', error);
        }
    }
}
}}),
"[project]/src/domains/intelligent-planning/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 智能规划域统一导出
 * 提供智能规划相关的所有功能
 */ // ============================================================================
// 算法导出
// ============================================================================
__turbopack_context__.s({
    "createAlgorithmCoordinator": (()=>createAlgorithmCoordinator),
    "createBalanceAlgorithm": (()=>createBalanceAlgorithm),
    "createFixAlgorithm": (()=>createFixAlgorithm),
    "createPlanningAlgorithm": (()=>createPlanningAlgorithm),
    "createPlanningService": (()=>createPlanningService),
    "createTimeAdjustmentAlgorithm": (()=>createTimeAdjustmentAlgorithm),
    "getDefaultPlanningService": (()=>getDefaultPlanningService),
    "getPlanningDomainFeatures": (()=>getPlanningDomainFeatures),
    "resetDefaultPlanningService": (()=>resetDefaultPlanningService),
    "runAlgorithmBenchmark": (()=>runAlgorithmBenchmark),
    "validatePlanningServiceHealth": (()=>validatePlanningServiceHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-ssr] (ecmascript)");
// ============================================================================
// 协调器导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-ssr] (ecmascript)");
// ============================================================================
// 服务导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
function createPlanningService() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PlanningService"]();
}
function createAlgorithmCoordinator() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlgorithmCoordinator"]();
}
function createPlanningAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PlanningAlgorithm"]();
}
function createBalanceAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BalanceAlgorithm"]();
}
function createFixAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FixAlgorithm"]();
}
function createTimeAdjustmentAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TimeAdjustmentAlgorithm"]();
}
// ============================================================================
// 默认实例（单例模式）
// ============================================================================
let defaultPlanningService = null;
function getDefaultPlanningService() {
    if (!defaultPlanningService) {
        defaultPlanningService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PlanningService"]();
    }
    return defaultPlanningService;
}
function resetDefaultPlanningService() {
    defaultPlanningService = null;
}
async function validatePlanningServiceHealth() {
    const errors = [];
    let isHealthy = true;
    try {
        const service = getDefaultPlanningService();
        const status = service.getServiceStatus();
        if (!status.isHealthy) {
            errors.push('规划服务状态异常');
            isHealthy = false;
        }
    } catch (error) {
        errors.push(`规划服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        isHealthy = false;
    }
    return {
        isHealthy,
        errors,
        timestamp: new Date()
    };
}
function getPlanningDomainFeatures() {
    return {
        algorithms: [
            'PlanningAlgorithm - 智能时间规划',
            'BalanceAlgorithm - 生活平衡分析',
            'FixAlgorithm - 推迟任务修复',
            'TimeAdjustmentAlgorithm - 动态时间调整'
        ],
        services: [
            'PlanningService - 统一规划服务接口',
            'AlgorithmCoordinator - 算法协调管理'
        ],
        capabilities: [
            '基于四象限的智能任务排序',
            '考虑任务类型的时间分配',
            '生活平衡状况分析和建议',
            '推迟任务检测和修复建议',
            '任务超时的动态时间调整',
            '综合的每日规划洞察',
            '个性化的时间管理建议'
        ]
    };
}
async function runAlgorithmBenchmark() {
    const coordinator = createAlgorithmCoordinator();
    // 模拟测试数据
    const mockTasks = [
        {
            id: '1',
            userId: 'test',
            title: '测试任务1',
            category: 'work',
            importance: 4,
            urgency: 3,
            deadline: new Date(),
            estimatedDuration: 60,
            status: 'pending',
            postponeCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        }
    ];
    const startTime = Date.now();
    // 测试规划算法
    const planningStart = Date.now();
    await coordinator.generateDailySchedule(mockTasks);
    const planningTime = Date.now() - planningStart;
    // 测试平衡算法
    const balanceStart = Date.now();
    await coordinator.analyzeLifeBalance('test');
    const balanceTime = Date.now() - balanceStart;
    // 测试修复算法
    const fixStart = Date.now();
    coordinator.analyzePostponedTasks(mockTasks);
    const fixTime = Date.now() - fixStart;
    // 测试调整算法
    const adjustmentStart = Date.now();
    coordinator.predictAdjustmentImpact(30, [], 0);
    const adjustmentTime = Date.now() - adjustmentStart;
    const totalTime = Date.now() - startTime;
    return {
        planningTime,
        balanceTime,
        fixTime,
        adjustmentTime,
        totalTime
    };
}
}}),
"[project]/src/domains/intelligent-planning/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/domains/task-management/models/Task.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务模型
 * 定义任务的数据结构和相关类型
 */ __turbopack_context__.s({
    "mapRecordToTask": (()=>mapRecordToTask),
    "mapTaskToRecord": (()=>mapTaskToRecord)
});
function mapRecordToTask(record) {
    return {
        id: record.id,
        userId: record.user_id,
        title: record.title,
        description: record.description,
        category: record.category,
        importance: record.importance,
        urgency: record.urgency,
        deadline: new Date(record.deadline),
        estimatedDuration: record.estimated_duration,
        status: record.status,
        postponeCount: record.postpone_count,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at)
    };
}
function mapTaskToRecord(task) {
    return {
        user_id: task.userId,
        title: task.title,
        description: task.description || '',
        category: task.category,
        importance: task.importance,
        urgency: task.urgency,
        deadline: task.deadline.toISOString(),
        estimated_duration: task.estimatedDuration,
        status: task.status || 'pending',
        postpone_count: task.postponeCount || 0
    };
}
}}),
"[project]/src/domains/task-management/repositories/TaskRepository.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务仓储层
 * 负责任务数据的持久化操作
 */ __turbopack_context__.s({
    "TaskRepository": (()=>TaskRepository)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-ssr] (ecmascript)");
;
;
class TaskRepository {
    /**
   * 获取用户的所有任务
   */ async findByUserId(userId) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', userId).order('created_at', {
                ascending: false
            });
            if (error) {
                return {
                    success: false,
                    error: `获取任务失败: ${error.message}`,
                    data: []
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            return {
                success: true,
                data: tasks
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取任务时发生未知错误',
                data: []
            };
        }
    }
    /**
   * 根据ID获取任务
   */ async findById(id) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('id', id).single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return {
                        success: true,
                        data: null
                    };
                }
                return {
                    success: false,
                    error: `获取任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 创建新任务
   */ async create(taskData) {
        try {
            const record = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapTaskToRecord"])(taskData);
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').insert(record).select().single();
            if (error) {
                return {
                    success: false,
                    error: `创建任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '创建任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 更新任务
   */ async update(updateData) {
        try {
            const { id, ...updates } = updateData;
            // 构建更新数据
            const updateRecord = {};
            if (updates.title !== undefined) updateRecord.title = updates.title;
            if (updates.description !== undefined) updateRecord.description = updates.description;
            if (updates.category !== undefined) updateRecord.category = updates.category;
            if (updates.importance !== undefined) updateRecord.importance = updates.importance;
            if (updates.urgency !== undefined) updateRecord.urgency = updates.urgency;
            if (updates.deadline !== undefined) updateRecord.deadline = updates.deadline.toISOString();
            if (updates.estimatedDuration !== undefined) updateRecord.estimated_duration = updates.estimatedDuration;
            if (updates.status !== undefined) updateRecord.status = updates.status;
            if (updates.postponeCount !== undefined) updateRecord.postpone_count = updates.postponeCount;
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update(updateRecord).eq('id', id).select().single();
            if (error) {
                return {
                    success: false,
                    error: `更新任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '更新任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 删除任务
   */ async delete(id) {
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').delete().eq('id', id);
            if (error) {
                return {
                    success: false,
                    error: `删除任务失败: ${error.message}`,
                    data: undefined
                };
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '删除任务时发生未知错误',
                data: undefined
            };
        }
    }
    /**
   * 根据条件过滤任务
   */ async findByFilter(filter) {
        try {
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', filter.userId);
            // 应用状态过滤
            if (filter.status) {
                if (Array.isArray(filter.status)) {
                    query = query.in('status', filter.status);
                } else {
                    query = query.eq('status', filter.status);
                }
            }
            // 应用分类过滤
            if (filter.category) {
                if (Array.isArray(filter.category)) {
                    query = query.in('category', filter.category);
                } else {
                    query = query.eq('category', filter.category);
                }
            }
            // 应用日期范围过滤
            if (filter.startDate) {
                query = query.gte('deadline', filter.startDate.toISOString());
            }
            if (filter.endDate) {
                query = query.lte('deadline', filter.endDate.toISOString());
            }
            // 应用搜索条件
            if (filter.searchTerm) {
                query = query.or(`title.ilike.%${filter.searchTerm}%,description.ilike.%${filter.searchTerm}%`);
            }
            const { data, error } = await query.order('created_at', {
                ascending: false
            });
            if (error) {
                return {
                    success: false,
                    error: `过滤任务失败: ${error.message}`,
                    data: []
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            return {
                success: true,
                data: tasks
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '过滤任务时发生未知错误',
                data: []
            };
        }
    }
    /**
   * 分页获取任务
   */ async findWithPagination(userId, pagination, sort) {
        try {
            const { page, pageSize } = pagination;
            const offset = (page - 1) * pageSize;
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*', {
                count: 'exact'
            }).eq('user_id', userId);
            // 应用排序
            if (sort) {
                const column = this.mapSortOptionToColumn(sort.sortBy);
                query = query.order(column, {
                    ascending: sort.direction === 'asc'
                });
            } else {
                query = query.order('created_at', {
                    ascending: false
                });
            }
            // 应用分页
            query = query.range(offset, offset + pageSize - 1);
            const { data, error, count } = await query;
            if (error) {
                return {
                    success: false,
                    error: `分页获取任务失败: ${error.message}`,
                    data: null
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            const total = count || 0;
            const totalPages = Math.ceil(total / pageSize);
            return {
                success: true,
                data: {
                    tasks,
                    total,
                    page,
                    pageSize,
                    totalPages
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '分页获取任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 批量更新任务状态
   */ async batchUpdateStatus(taskIds, status) {
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update({
                status
            }).in('id', taskIds);
            if (error) {
                return {
                    success: false,
                    error: `批量更新任务状态失败: ${error.message}`,
                    data: undefined
                };
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '批量更新任务状态时发生未知错误',
                data: undefined
            };
        }
    }
    /**
   * 映射排序选项到数据库列名
   */ mapSortOptionToColumn(sortOption) {
        const mapping = {
            deadline: 'deadline',
            importance: 'importance',
            urgency: 'urgency',
            createdAt: 'created_at',
            estimatedDuration: 'estimated_duration'
        };
        return mapping[sortOption] || 'created_at';
    }
}
}}),
"[project]/src/domains/task-management/services/TaskValidationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务验证服务
 * 负责任务数据的验证和业务规则检查
 */ __turbopack_context__.s({
    "TaskValidationService": (()=>TaskValidationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <locals>");
;
class TaskValidationService {
    /**
   * 验证任务创建请求
   */ validateCreateRequest(request) {
        const errors = [];
        const warnings = [];
        // 验证必填字段
        if (!request.userId || request.userId.trim().length === 0) {
            errors.push('用户ID不能为空');
        }
        if (!request.title || request.title.trim().length === 0) {
            errors.push('任务标题不能为空');
        } else if (request.title.length > 100) {
            errors.push('任务标题不能超过100个字符');
        }
        // 验证分类
        if (!request.category || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskCategory"])(request.category)) {
            errors.push('请选择有效的任务分类');
        }
        // 验证重要性
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.importance)) {
            errors.push('重要性必须在1-5之间');
        }
        // 验证紧急性
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.urgency)) {
            errors.push('紧急性必须在1-5之间');
        }
        // 验证截止时间
        if (!request.deadline) {
            errors.push('请设置截止时间');
        } else {
            const now = new Date();
            if (request.deadline < now) {
                errors.push('截止时间不能早于当前时间');
            }
            // 警告：截止时间过于遥远
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            if (request.deadline > oneYearLater) {
                warnings.push('截止时间设置得较远，建议设置更近的时间以保持紧迫感');
            }
        }
        // 验证预估时长
        if (!request.estimatedDuration || request.estimatedDuration <= 0) {
            errors.push('预估时长必须大于0');
        } else if (request.estimatedDuration < 15) {
            warnings.push('预估时长少于15分钟，建议合并到其他任务中');
        } else if (request.estimatedDuration > 480) {
            warnings.push('预估时长超过8小时，建议分解为更小的任务');
        }
        // 验证描述长度
        if (request.description && request.description.length > 500) {
            errors.push('任务描述不能超过500个字符');
        }
        // 业务规则验证
        this.validateBusinessRules(request, errors, warnings);
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务更新请求
   */ validateUpdateRequest(request) {
        const errors = [];
        const warnings = [];
        // 验证ID
        if (!request.id || request.id.trim().length === 0) {
            errors.push('任务ID不能为空');
        }
        // 验证标题（如果提供）
        if (request.title !== undefined) {
            if (request.title.trim().length === 0) {
                errors.push('任务标题不能为空');
            } else if (request.title.length > 100) {
                errors.push('任务标题不能超过100个字符');
            }
        }
        // 验证分类（如果提供）
        if (request.category !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskCategory"])(request.category)) {
            errors.push('请选择有效的任务分类');
        }
        // 验证重要性（如果提供）
        if (request.importance !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.importance)) {
            errors.push('重要性必须在1-5之间');
        }
        // 验证紧急性（如果提供）
        if (request.urgency !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.urgency)) {
            errors.push('紧急性必须在1-5之间');
        }
        // 验证截止时间（如果提供）
        if (request.deadline !== undefined) {
            const now = new Date();
            if (request.deadline < now) {
                errors.push('截止时间不能早于当前时间');
            }
        }
        // 验证预估时长（如果提供）
        if (request.estimatedDuration !== undefined) {
            if (request.estimatedDuration <= 0) {
                errors.push('预估时长必须大于0');
            } else if (request.estimatedDuration > 480) {
                warnings.push('预估时长超过8小时，建议分解为更小的任务');
            }
        }
        // 验证状态（如果提供）
        if (request.status !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskStatus"])(request.status)) {
            errors.push('请选择有效的任务状态');
        }
        // 验证描述长度（如果提供）
        if (request.description !== undefined && request.description.length > 500) {
            errors.push('任务描述不能超过500个字符');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务完成请求
   */ validateCompleteRequest(request, task) {
        const errors = [];
        const warnings = [];
        // 验证任务状态
        if (task.status === 'completed') {
            errors.push('任务已经完成');
        }
        // 验证实际时长
        if (request.actualDuration !== undefined) {
            if (request.actualDuration <= 0) {
                errors.push('实际时长必须大于0');
            } else {
                // 检查实际时长与预估时长的差异
                const estimatedDuration = task.estimatedDuration;
                const ratio = request.actualDuration / estimatedDuration;
                if (ratio > 2) {
                    warnings.push('实际时长远超预估时长，建议调整未来类似任务的时间预估');
                } else if (ratio < 0.5) {
                    warnings.push('实际时长远少于预估时长，建议调整未来类似任务的时间预估');
                }
            }
        }
        // 验证满意度评分
        if (request.satisfactionScore !== undefined) {
            if (request.satisfactionScore < 1 || request.satisfactionScore > 5) {
                errors.push('满意度评分必须在1-5之间');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务推迟请求
   */ validatePostponeRequest(request, task) {
        const errors = [];
        const warnings = [];
        // 验证任务状态
        if (task.status === 'completed') {
            errors.push('已完成的任务不能推迟');
        }
        // 验证推迟次数
        if (task.postponeCount >= 3) {
            warnings.push('该任务已推迟多次，建议重新评估任务的必要性或分解任务');
        }
        // 验证新的截止时间
        if (request.newDeadline) {
            const now = new Date();
            if (request.newDeadline <= now) {
                errors.push('新的截止时间必须晚于当前时间');
            }
            if (request.newDeadline <= task.deadline) {
                errors.push('新的截止时间必须晚于原截止时间');
            }
        }
        // 验证推迟原因
        if (request.reason && request.reason.length > 200) {
            errors.push('推迟原因不能超过200个字符');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证业务规则
   */ validateBusinessRules(request, errors, warnings) {
        // 规则1：娱乐任务不应该设置过高的重要性和紧急性
        if (request.category === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT) {
            if (request.importance >= 4 && request.urgency >= 4) {
                warnings.push('娱乐任务通常不需要设置过高的重要性和紧急性');
            }
        }
        // 规则2：工作任务在非工作时间的警告
        if (request.category === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK) {
            const deadlineHour = request.deadline.getHours();
            if (deadlineHour < 9 || deadlineHour > 18) {
                warnings.push('工作任务的截止时间设置在非工作时间，请确认是否合理');
            }
        }
        // 规则3：高重要性但低紧急性的任务应该有合理的时间安排
        if (request.importance >= 4 && request.urgency <= 2) {
            const timeToDeadline = request.deadline.getTime() - new Date().getTime();
            const daysToDeadline = timeToDeadline / (1000 * 60 * 60 * 24);
            if (daysToDeadline < 1) {
                warnings.push('重要但不紧急的任务截止时间过近，可能影响执行质量');
            }
        }
        // 规则4：短时间任务的合理性检查
        if (request.estimatedDuration < 30) {
            const timeToDeadline = request.deadline.getTime() - new Date().getTime();
            const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
            if (hoursToDeadline > 24 && request.urgency >= 4) {
                warnings.push('短时间任务设置了较远的截止时间但标记为紧急，请检查设置是否合理');
            }
        }
    }
    /**
   * 验证任务状态转换的合法性
   */ validateStatusTransition(currentStatus, newStatus) {
        const errors = [];
        const warnings = [];
        // 定义合法的状态转换
        const validTransitions = {
            'pending': [
                'in-progress',
                'postponed',
                'completed'
            ],
            'in-progress': [
                'completed',
                'postponed',
                'pending'
            ],
            'completed': [],
            'postponed': [
                'pending',
                'in-progress',
                'completed'
            ]
        };
        if (!validTransitions[currentStatus].includes(newStatus)) {
            errors.push(`不能从状态 "${currentStatus}" 转换到 "${newStatus}"`);
        }
        // 特殊情况的警告
        if (currentStatus === 'completed' && newStatus !== 'completed') {
            warnings.push('重新激活已完成的任务，请确认是否必要');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
}}),
"[project]/src/domains/task-management/services/TaskService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务服务
 * 提供任务管理的核心业务逻辑
 */ __turbopack_context__.s({
    "TaskService": (()=>TaskService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-ssr] (ecmascript)");
;
;
;
class TaskService {
    repository;
    validationService;
    constructor(){
        this.repository = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskRepository"]();
        this.validationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskValidationService"]();
    }
    // ============================================================================
    // 基础 CRUD 操作
    // ============================================================================
    /**
   * 获取用户的所有任务
   */ async getUserTasks(userId) {
        if (!userId) {
            return {
                success: false,
                error: '用户ID不能为空',
                data: []
            };
        }
        return await this.repository.findByUserId(userId);
    }
    /**
   * 根据ID获取任务
   */ async getTaskById(id) {
        if (!id) {
            return {
                success: false,
                error: '任务ID不能为空',
                data: null
            };
        }
        return await this.repository.findById(id);
    }
    /**
   * 创建新任务
   */ async createTask(request) {
        // 验证请求数据
        const validation = this.validationService.validateCreateRequest(request);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 创建任务
        const result = await this.repository.create(request);
        if (result.success && validation.warnings.length > 0) {
            console.warn('任务创建警告:', validation.warnings);
        }
        return result;
    }
    /**
   * 更新任务
   */ async updateTask(request) {
        // 验证请求数据
        const validation = this.validationService.validateUpdateRequest(request);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 如果更新状态，验证状态转换
        if (request.status) {
            const currentTaskResult = await this.repository.findById(request.id);
            if (!currentTaskResult.success || !currentTaskResult.data) {
                return {
                    success: false,
                    error: '任务不存在',
                    data: null
                };
            }
            const statusValidation = this.validationService.validateStatusTransition(currentTaskResult.data.status, request.status);
            if (!statusValidation.isValid) {
                return {
                    success: false,
                    error: statusValidation.errors.join('; '),
                    data: null
                };
            }
        }
        return await this.repository.update(request);
    }
    /**
   * 删除任务
   */ async deleteTask(id) {
        if (!id) {
            return {
                success: false,
                error: '任务ID不能为空',
                data: undefined
            };
        }
        // 检查任务是否存在
        const taskResult = await this.repository.findById(id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: undefined
            };
        }
        return await this.repository.delete(id);
    }
    // ============================================================================
    // 任务状态管理
    // ============================================================================
    /**
   * 完成任务
   */ async completeTask(request) {
        // 获取当前任务
        const taskResult = await this.repository.findById(request.id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: null
            };
        }
        const task = taskResult.data;
        // 验证完成请求
        const validation = this.validationService.validateCompleteRequest(request, task);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 更新任务状态
        const updateResult = await this.repository.update({
            id: request.id,
            status: 'completed'
        });
        if (updateResult.success && validation.warnings.length > 0) {
            console.warn('任务完成警告:', validation.warnings);
        }
        return updateResult;
    }
    /**
   * 推迟任务
   */ async postponeTask(request) {
        // 获取当前任务
        const taskResult = await this.repository.findById(request.id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: null
            };
        }
        const task = taskResult.data;
        // 验证推迟请求
        const validation = this.validationService.validatePostponeRequest(request, task);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 更新任务
        const updateData = {
            id: request.id,
            status: 'postponed',
            postponeCount: task.postponeCount + 1
        };
        if (request.newDeadline) {
            updateData.deadline = request.newDeadline;
        }
        const updateResult = await this.repository.update(updateData);
        if (updateResult.success && validation.warnings.length > 0) {
            console.warn('任务推迟警告:', validation.warnings);
        }
        return updateResult;
    }
    /**
   * 开始任务
   */ async startTask(id) {
        return await this.updateTask({
            id,
            status: 'in-progress'
        });
    }
    /**
   * 暂停任务
   */ async pauseTask(id) {
        return await this.updateTask({
            id,
            status: 'pending'
        });
    }
    // ============================================================================
    // 任务查询和过滤
    // ============================================================================
    /**
   * 根据条件过滤任务
   */ async getTasksByFilter(filter) {
        return await this.repository.findByFilter(filter);
    }
    /**
   * 分页获取任务
   */ async getTasksWithPagination(userId, pagination, sort) {
        if (!userId) {
            return {
                success: false,
                error: '用户ID不能为空',
                data: null
            };
        }
        return await this.repository.findWithPagination(userId, pagination, sort);
    }
    /**
   * 获取今日任务
   */ async getTodayTasks(userId) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const filter = {
            userId,
            status: [
                'pending',
                'in-progress'
            ],
            endDate: tomorrow
        };
        return await this.getTasksByFilter(filter);
    }
    /**
   * 获取过期任务
   */ async getOverdueTasks(userId) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return tasksResult;
        }
        const overdueTasks = tasksResult.data.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task) && task.status !== 'completed');
        return {
            success: true,
            data: overdueTasks
        };
    }
    /**
   * 获取即将到期的任务
   */ async getDueSoonTasks(userId, hoursThreshold = 24) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return tasksResult;
        }
        const dueSoonTasks = tasksResult.data.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskDueSoon"])(task, hoursThreshold) && task.status !== 'completed');
        return {
            success: true,
            data: dueSoonTasks
        };
    }
    // ============================================================================
    // 任务统计
    // ============================================================================
    /**
   * 获取任务统计信息
   */ async getTaskStatistics(userId) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return {
                success: false,
                error: tasksResult.error,
                data: null
            };
        }
        const tasks = tasksResult.data;
        // 按状态统计
        const byStatus = tasks.reduce((acc, task)=>{
            acc[task.status] = (acc[task.status] || 0) + 1;
            return acc;
        }, {});
        // 按分类统计
        const byCategory = tasks.reduce((acc, task)=>{
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});
        // 按象限统计
        const byQuadrant = tasks.reduce((acc, task)=>{
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            acc[quadrant] = (acc[quadrant] || 0) + 1;
            return acc;
        }, {});
        // 过期和即将到期任务数量
        const overdue = tasks.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task) && task.status !== 'completed').length;
        const dueSoon = tasks.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskDueSoon"])(task) && task.status !== 'completed').length;
        // 平均完成时间（这里简化处理，实际应该从完成记录中计算）
        const completedTasks = tasks.filter((task)=>task.status === 'completed');
        const averageCompletionTime = completedTasks.length > 0 ? completedTasks.reduce((sum, task)=>sum + task.estimatedDuration, 0) / completedTasks.length : 0;
        const statistics = {
            total: tasks.length,
            byStatus: byStatus,
            byCategory: byCategory,
            byQuadrant: byQuadrant,
            overdue,
            dueSoon,
            averageCompletionTime
        };
        return {
            success: true,
            data: statistics
        };
    }
    // ============================================================================
    // 批量操作
    // ============================================================================
    /**
   * 批量更新任务状态
   */ async batchUpdateStatus(taskIds, status) {
        if (!taskIds || taskIds.length === 0) {
            return {
                success: false,
                error: '任务ID列表不能为空',
                data: undefined
            };
        }
        return await this.repository.batchUpdateStatus(taskIds, status);
    }
    /**
   * 批量删除任务
   */ async batchDeleteTasks(taskIds) {
        if (!taskIds || taskIds.length === 0) {
            return {
                success: false,
                error: '任务ID列表不能为空',
                data: undefined
            };
        }
        try {
            for (const id of taskIds){
                const result = await this.repository.delete(id);
                if (!result.success) {
                    return {
                        success: false,
                        error: `删除任务 ${id} 失败: ${result.error}`,
                        data: undefined
                    };
                }
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '批量删除任务时发生未知错误',
                data: undefined
            };
        }
    }
}
}}),
"[project]/src/domains/task-management/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务管理域统一导出
 * 提供任务管理相关的所有功能
 */ // ============================================================================
// 模型导出
// ============================================================================
__turbopack_context__.s({
    "createSampleTaskData": (()=>createSampleTaskData),
    "createTaskRepository": (()=>createTaskRepository),
    "createTaskService": (()=>createTaskService),
    "createTaskValidationService": (()=>createTaskValidationService),
    "getDefaultTaskService": (()=>getDefaultTaskService),
    "getTaskManagementConfig": (()=>getTaskManagementConfig),
    "getTaskManagementDomainFeatures": (()=>getTaskManagementDomainFeatures),
    "resetDefaultTaskService": (()=>resetDefaultTaskService),
    "runTaskManagementBenchmark": (()=>runTaskManagementBenchmark),
    "validateTaskServiceHealth": (()=>validateTaskServiceHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-ssr] (ecmascript)");
// ============================================================================
// 仓储导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-ssr] (ecmascript)");
// ============================================================================
// 服务导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
function createTaskService() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskService"]();
}
function createTaskRepository() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskRepository"]();
}
function createTaskValidationService() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskValidationService"]();
}
// ============================================================================
// 默认实例（单例模式）
// ============================================================================
let defaultTaskService = null;
function getDefaultTaskService() {
    if (!defaultTaskService) {
        defaultTaskService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskService"]();
    }
    return defaultTaskService;
}
function resetDefaultTaskService() {
    defaultTaskService = null;
}
async function validateTaskServiceHealth() {
    const errors = [];
    let isHealthy = true;
    try {
        const service = getDefaultTaskService();
        // 尝试创建一个测试验证
        const testRequest = {
            userId: 'test',
            title: 'Test Task',
            category: 'work',
            importance: 3,
            urgency: 3,
            deadline: new Date(),
            estimatedDuration: 60
        };
        const validation = service['validationService'].validateCreateRequest(testRequest);
        if (!validation) {
            errors.push('任务验证服务不可用');
            isHealthy = false;
        }
    } catch (error) {
        errors.push(`任务服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        isHealthy = false;
    }
    return {
        isHealthy,
        errors,
        timestamp: new Date()
    };
}
function getTaskManagementDomainFeatures() {
    return {
        models: [
            'Task - 核心任务模型',
            'TaskRecord - 数据库记录映射',
            'CreateTaskRequest - 任务创建请求',
            'UpdateTaskRequest - 任务更新请求',
            'CompleteTaskRequest - 任务完成请求',
            'PostponeTaskRequest - 任务推迟请求',
            'TaskFilter - 任务过滤条件',
            'TaskStatistics - 任务统计信息'
        ],
        repositories: [
            'TaskRepository - 任务数据持久化'
        ],
        services: [
            'TaskService - 核心任务业务逻辑',
            'TaskValidationService - 任务数据验证'
        ],
        capabilities: [
            '完整的任务 CRUD 操作',
            '任务状态生命周期管理',
            '任务数据验证和业务规则检查',
            '任务过滤、排序和分页',
            '任务统计和分析',
            '批量任务操作',
            '任务完成和推迟处理',
            '过期和即将到期任务检测',
            '任务状态转换验证',
            '业务规则和警告提示'
        ]
    };
}
async function runTaskManagementBenchmark() {
    const service = createTaskService();
    const validationService = createTaskValidationService();
    // 模拟测试数据
    const testRequest = {
        userId: 'benchmark-test',
        title: '性能测试任务',
        category: 'work',
        importance: 3,
        urgency: 3,
        deadline: new Date(),
        estimatedDuration: 60
    };
    const startTime = Date.now();
    // 测试验证性能
    const validationStart = Date.now();
    validationService.validateCreateRequest(testRequest);
    const validationTime = Date.now() - validationStart;
    // 注意：这里只测试验证逻辑，不执行实际的数据库操作
    // 实际的 CRUD 操作需要数据库连接
    const createTime = 0; // 模拟值
    const readTime = 0; // 模拟值
    const updateTime = 0; // 模拟值
    const deleteTime = 0; // 模拟值
    const totalTime = Date.now() - startTime;
    return {
        createTime,
        readTime,
        updateTime,
        deleteTime,
        validationTime,
        totalTime
    };
}
function getTaskManagementConfig() {
    return {
        maxTitleLength: 100,
        maxDescriptionLength: 500,
        maxPostponeCount: 5,
        defaultPageSize: 20,
        maxPageSize: 100
    };
}
function createSampleTaskData() {
    return [
        {
            userId: 'sample-user',
            title: '完成项目报告',
            description: '撰写季度项目总结报告',
            category: 'work',
            importance: 4,
            urgency: 3,
            deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            estimatedDuration: 120
        },
        {
            userId: 'sample-user',
            title: '学习新技术',
            description: '学习 React 18 的新特性',
            category: 'improvement',
            importance: 3,
            urgency: 2,
            deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
            estimatedDuration: 180
        },
        {
            userId: 'sample-user',
            title: '看电影放松',
            description: '观看最新上映的电影',
            category: 'entertainment',
            importance: 2,
            urgency: 1,
            deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
            estimatedDuration: 120
        }
    ];
}
}}),
"[project]/src/domains/task-management/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/store/useTaskStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTaskStore": (()=>useTaskStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
const useTaskStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        // Initial state
        tasks: [],
        dailySchedule: null,
        balanceAnalysis: null,
        postponedAlerts: [],
        loading: false,
        error: null,
        // Service instances
        planningService: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultPlanningService"])(),
        taskService: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultTaskService"])(),
        // Fetch tasks
        fetchTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.getUserTasks(userId);
                if (result.success) {
                    set({
                        tasks: result.data,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to fetch tasks',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Create task
        createTask: async (taskData)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.createTask(taskData);
                if (result.success) {
                    set((state)=>({
                            tasks: [
                                result.data,
                                ...state.tasks
                            ],
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to create task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Update task
        updateTask: async (id, updates)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.updateTask({
                    id,
                    ...updates
                });
                if (result.success) {
                    set((state)=>({
                            tasks: state.tasks.map((task)=>task.id === id ? result.data : task),
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to update task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Delete task
        deleteTask: async (id)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.deleteTask(id);
                if (result.success) {
                    set((state)=>({
                            tasks: state.tasks.filter((task)=>task.id !== id),
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to delete task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Complete task
        completeTask: async (id, actualDuration, satisfaction)=>{
            try {
                const { taskService, planningService } = get();
                const task = get().tasks.find((t)=>t.id === id);
                if (!task) throw new Error('Task not found');
                // Complete task using task service
                const result = await taskService.completeTask({
                    id,
                    actualDuration,
                    satisfactionScore: satisfaction
                });
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                    // Update daily stats
                    await planningService.updateActivityStats(task.userId, task.category, actualDuration || task.estimatedDuration);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Postpone task
        postponeTask: async (id, reason)=>{
            try {
                const { taskService } = get();
                const result = await taskService.postponeTask({
                    id,
                    reason
                });
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Start task
        startTask: async (id)=>{
            try {
                const { taskService } = get();
                const result = await taskService.startTask(id);
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to start task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Pause task
        pauseTask: async (id)=>{
            try {
                const { taskService } = get();
                const result = await taskService.pauseTask(id);
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to pause task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Generate daily schedule
        generateDailySchedule: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { tasks, planningService } = get();
                const userTasks = tasks.filter((task)=>task.userId === userId);
                // 获取用户时间配置
                let userTimeConfig = null;
                try {
                    const { data: userProfile } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('user_profiles').select('time_config').eq('id', userId).single();
                    userTimeConfig = userProfile?.time_config;
                } catch (error) {
                    console.log('No user time config found, using defaults');
                }
                // 使用新的规划服务
                const result = await planningService.generateDailyPlan(userId, userTasks, userTimeConfig);
                if (result.success) {
                    set({
                        dailySchedule: result.data.schedule,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to generate schedule',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze balance
        analyzeBalance: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { planningService } = get();
                const result = await planningService.getBalanceAnalysis(userId);
                if (result.success) {
                    set({
                        balanceAnalysis: result.data,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to analyze balance',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze postponed tasks
        analyzePostponedTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { tasks, planningService } = get();
                const userTasks = tasks.filter((task)=>task.userId === userId);
                const result = await planningService.getPostponedTasksAnalysis(userTasks);
                if (result.success) {
                    set({
                        postponedAlerts: result.data.alerts,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to analyze postponed tasks',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Utility actions
        setLoading: (loading)=>set({
                loading
            }),
        setError: (error)=>set({
                error
            }),
        clearError: ()=>set({
                error: null
            })
    }));
}}),
"[project]/src/components/tasks/TaskItem.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TaskItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-ssr] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-ssr] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-ssr] (ecmascript) <export default as Pause>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-ssr] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-ssr] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js [app-ssr] (ecmascript) <export default as MoreVertical>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function TaskItem({ task, onEdit, onDelete, onStatusChange, onStart, onComplete }) {
    const [showActions, setShowActions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const categoryColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCategoryColor"])(task.category);
    const statusColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStatusColor"])(task.status);
    const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
    const isDueSoon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTaskDueSoon"])(task);
    const formatDuration = (minutes)=>{
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };
    const formatDeadline = (date)=>{
        const now = new Date();
        const diffTime = date.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays < 0) {
            return `逾期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
            return '今天到期';
        } else if (diffDays === 1) {
            return '明天到期';
        } else if (diffDays <= 7) {
            return `${diffDays} 天后到期`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    };
    const getQuadrantInfo = (importance, urgency)=>{
        if (importance >= 4 && urgency >= 4) {
            return {
                label: 'Q1',
                color: 'bg-red-100 text-red-800',
                desc: '重要紧急'
            };
        } else if (importance >= 4 && urgency < 4) {
            return {
                label: 'Q2',
                color: 'bg-blue-100 text-blue-800',
                desc: '重要不紧急'
            };
        } else if (importance < 4 && urgency >= 4) {
            return {
                label: 'Q3',
                color: 'bg-yellow-100 text-yellow-800',
                desc: '不重要紧急'
            };
        } else {
            return {
                label: 'Q4',
                color: 'bg-gray-100 text-gray-800',
                desc: '不重要不紧急'
            };
        }
    };
    const quadrant = getQuadrantInfo(task.importance, task.urgency);
    const handleStatusChange = (newStatus)=>{
        if (newStatus === 'completed') {
            onComplete(task.id);
        } else if (newStatus === 'in-progress') {
            onStart(task.id);
        } else {
            onStatusChange(task.id, newStatus);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `
      bg-white rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow duration-200
      ${isOverdue ? 'border-l-red-500 bg-red-50' : isDueSoon ? 'border-l-yellow-500 bg-yellow-50' : `border-l-${categoryColor}-500`}
    `,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "p-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start justify-between mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2 mb-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-medium text-gray-900 truncate",
                                            children: task.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 114,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `px-2 py-1 text-xs font-medium rounded-full ${quadrant.color}`,
                                            children: quadrant.label
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 117,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                task.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-gray-600 line-clamp-2",
                                    children: task.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 122,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowActions(!showActions),
                                    className: "p-1 rounded-full hover:bg-gray-100 transition-colors",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__["MoreVertical"], {
                                        className: "h-4 w-4 text-gray-500"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                        lineNumber: 134,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 130,
                                    columnNumber: 13
                                }, this),
                                showActions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute right-0 top-8 bg-white rounded-md shadow-lg border z-10 min-w-[120px]",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>{
                                                onEdit(task);
                                                setShowActions(false);
                                            },
                                            className: "w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                    className: "h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                                    lineNumber: 146,
                                                    columnNumber: 19
                                                }, this),
                                                "编辑"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 139,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>{
                                                onDelete(task.id);
                                                setShowActions(false);
                                            },
                                            className: "w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                    className: "h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 19
                                                }, this),
                                                "删除"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 149,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 138,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 129,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4 text-sm text-gray-600 mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: `w-2 h-2 rounded-full bg-${categoryColor}-500`
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 167,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCategoryName"])(task.category)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 168,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 166,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "h-3 w-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 172,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: formatDuration(task.estimatedDuration)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 173,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 171,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex items-center gap-1 ${isOverdue ? 'text-red-600' : isDueSoon ? 'text-yellow-600' : ''}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                    className: "h-3 w-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 177,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: formatDeadline(task.deadline)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 178,
                                    columnNumber: 13
                                }, this),
                                isOverdue && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                                    className: "h-3 w-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 179,
                                    columnNumber: 27
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 176,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                    lineNumber: 165,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: `px-2 py-1 text-xs font-medium rounded-full ${statusColor}`,
                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStatusName"])(task.status)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this),
                                task.postponeCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full",
                                    children: [
                                        "推迟 ",
                                        task.postponeCount,
                                        " 次"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 190,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 185,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                task.status === 'pending' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handleStatusChange('in-progress'),
                                    className: "p-1 rounded-full hover:bg-green-100 text-green-600 transition-colors",
                                    title: "开始任务",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                        lineNumber: 204,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 199,
                                    columnNumber: 15
                                }, this),
                                task.status === 'in-progress' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleStatusChange('pending'),
                                            className: "p-1 rounded-full hover:bg-yellow-100 text-yellow-600 transition-colors",
                                            title: "暂停任务",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__["Pause"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                                lineNumber: 215,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 210,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleStatusChange('completed'),
                                            className: "p-1 rounded-full hover:bg-blue-100 text-blue-600 transition-colors",
                                            title: "完成任务",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                                lineNumber: 222,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                            lineNumber: 217,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true),
                                task.status === 'completed' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handleStatusChange('pending'),
                                    className: "p-1 rounded-full hover:bg-gray-100 text-gray-600 transition-colors",
                                    title: "重新激活",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                        lineNumber: 233,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                                    lineNumber: 228,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskItem.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskItem.tsx",
                    lineNumber: 184,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/tasks/TaskItem.tsx",
            lineNumber: 109,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/tasks/TaskItem.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/QuadrantSelector.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>QuadrantSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function QuadrantSelector({ importance, urgency, onChange }) {
    const [selectedQuadrant, setSelectedQuadrant] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const currentQuadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classifyQuadrant"])(importance, urgency);
    // 象限配置
    const quadrants = [
        {
            id: 1,
            title: '重要且紧急',
            description: '立即处理',
            color: 'bg-red-100 border-red-300 hover:bg-red-200',
            selectedColor: 'bg-red-200 border-red-500',
            textColor: 'text-red-800',
            importance: 5,
            urgency: 5,
            position: 'top-right'
        },
        {
            id: 2,
            title: '重要不紧急',
            description: '计划安排',
            color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',
            selectedColor: 'bg-blue-200 border-blue-500',
            textColor: 'text-blue-800',
            importance: 5,
            urgency: 2,
            position: 'top-left'
        },
        {
            id: 3,
            title: '不重要但紧急',
            description: '委托处理',
            color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',
            selectedColor: 'bg-yellow-200 border-yellow-500',
            textColor: 'text-yellow-800',
            importance: 2,
            urgency: 5,
            position: 'bottom-right'
        },
        {
            id: 4,
            title: '不重要不紧急',
            description: '减少或删除',
            color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',
            selectedColor: 'bg-gray-200 border-gray-500',
            textColor: 'text-gray-800',
            importance: 2,
            urgency: 2,
            position: 'bottom-left'
        }
    ];
    const handleQuadrantClick = (quadrant)=>{
        setSelectedQuadrant(quadrant.id);
        onChange(quadrant.importance, quadrant.urgency);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm font-medium text-gray-700 mb-2",
                children: "任务优先级（点击象限选择）"
            }, void 0, false, {
                fileName: "[project]/src/components/QuadrantSelector.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative w-full max-w-md mx-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium",
                        children: "紧急程度 →"
                    }, void 0, false, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium",
                        children: "重要程度 →"
                    }, void 0, false, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 82,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 gap-2 w-80 h-80",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}
              ${quadrants[1].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[1]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 98,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "不紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 99,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "计划安排"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 100,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 II"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 101,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}
              ${quadrants[0].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[0]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 114,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 115,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "立即处理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 116,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 I"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}
              ${quadrants[3].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[3]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "不重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 130,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "不紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 131,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "减少删除"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 132,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 IV"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 133,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}
              ${quadrants[2].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[2]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "不重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 146,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "委托处理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 148,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 III"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 149,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 87,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-600",
                                children: [
                                    "当前选择：",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            "象限 ",
                                            currentQuadrant
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 156,
                                        columnNumber: 18
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 155,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500 mt-1",
                                children: [
                                    "重要性: ",
                                    importance,
                                    "/5 | 紧急性: ",
                                    urgency,
                                    "/5"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 158,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/QuadrantSelector.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/QuadrantSelector.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/tasks/TaskEditForm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TaskEditForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-ssr] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$QuadrantSelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/QuadrantSelector.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function TaskEditForm({ task, isOpen, onClose, onSave }) {
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        title: '',
        description: '',
        category: 'work',
        importance: 3,
        urgency: 3,
        deadline: '',
        estimatedDuration: 1
    });
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // 当任务数据变化时更新表单
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (task) {
            setFormData({
                title: task.title,
                description: task.description || '',
                category: task.category,
                importance: task.importance,
                urgency: task.urgency,
                deadline: task.deadline.toISOString().slice(0, 16),
                estimatedDuration: Math.round(task.estimatedDuration / 60 * 10) / 10 // 转换为小时并保留一位小数
            });
        }
    }, [
        task
    ]);
    const handleChange = (e)=>{
        const { name, value } = e.target;
        setFormData((prev)=>({
                ...prev,
                [name]: name === 'importance' || name === 'urgency' ? parseInt(value) : name === 'estimatedDuration' ? parseFloat(value) : value
            }));
        // 清除对应字段的错误
        if (errors[name]) {
            setErrors((prev)=>({
                    ...prev,
                    [name]: ''
                }));
        }
    };
    const handleQuadrantChange = (importance, urgency)=>{
        setFormData((prev)=>({
                ...prev,
                importance,
                urgency
            }));
    };
    const validateForm = ()=>{
        const newErrors = {};
        if (!formData.title.trim()) {
            newErrors.title = '任务标题不能为空';
        }
        if (!formData.deadline) {
            newErrors.deadline = '截止时间不能为空';
        } else {
            const deadlineDate = new Date(formData.deadline);
            const now = new Date();
            if (deadlineDate <= now) {
                newErrors.deadline = '截止时间必须晚于当前时间';
            }
        }
        if (formData.estimatedDuration <= 0) {
            newErrors.estimatedDuration = '预估时长必须大于0';
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!validateForm()) {
            return;
        }
        setLoading(true);
        try {
            const updateData = {
                title: formData.title.trim(),
                description: formData.description.trim(),
                category: formData.category,
                importance: formData.importance,
                urgency: formData.urgency,
                deadline: new Date(formData.deadline),
                estimatedDuration: Math.round(formData.estimatedDuration * 60) // 转换为分钟
            };
            await onSave(updateData);
            onClose();
        } catch (error) {
            console.error('Failed to save task:', error);
            setErrors({
                submit: '保存失败，请重试'
            });
        } finally{
            setLoading(false);
        }
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between p-6 border-b",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-gray-900",
                            children: task ? '编辑任务' : '新建任务'
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "p-2 hover:bg-gray-100 rounded-full transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-5 w-5 text-gray-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                lineNumber: 145,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 141,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: "p-6 space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "title",
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "任务标题 *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 153,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "title",
                                    name: "title",
                                    value: formData.title,
                                    onChange: handleChange,
                                    className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.title ? 'border-red-300' : 'border-gray-300'}`,
                                    placeholder: "请输入任务标题"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, this),
                                errors.title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-1 text-sm text-red-600",
                                    children: errors.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 167,
                                    columnNumber: 30
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 152,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "description",
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "任务描述"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 172,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                    id: "description",
                                    name: "description",
                                    rows: 3,
                                    value: formData.description,
                                    onChange: handleChange,
                                    className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                                    placeholder: "请输入任务描述（可选）"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 175,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 171,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "category",
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "任务分类 *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 188,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    id: "category",
                                    name: "category",
                                    value: formData.category,
                                    onChange: handleChange,
                                    className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "work",
                                            children: "工作"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 198,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "improvement",
                                            children: "自我提升"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 199,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "entertainment",
                                            children: "娱乐"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 187,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "优先级设置 *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 206,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$QuadrantSelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    importance: formData.importance,
                                    urgency: formData.urgency,
                                    onChange: handleQuadrantChange
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 209,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 205,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "deadline",
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "截止时间 *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 218,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "datetime-local",
                                            id: "deadline",
                                            name: "deadline",
                                            value: formData.deadline,
                                            onChange: handleChange,
                                            className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.deadline ? 'border-red-300' : 'border-gray-300'}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 222,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                            className: "absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 232,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 221,
                                    columnNumber: 13
                                }, this),
                                errors.deadline && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-1 text-sm text-red-600",
                                    children: errors.deadline
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 234,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 217,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "estimatedDuration",
                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                    children: "预估时长（小时）*"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 239,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "number",
                                            id: "estimatedDuration",
                                            name: "estimatedDuration",
                                            min: "0.1",
                                            step: "0.1",
                                            value: formData.estimatedDuration,
                                            onChange: handleChange,
                                            className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.estimatedDuration ? 'border-red-300' : 'border-gray-300'}`,
                                            placeholder: "1.0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 243,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                            className: "absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 242,
                                    columnNumber: 13
                                }, this),
                                errors.estimatedDuration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-1 text-sm text-red-600",
                                    children: errors.estimatedDuration
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 258,
                                    columnNumber: 42
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 238,
                            columnNumber: 11
                        }, this),
                        errors.submit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-3 bg-red-50 border border-red-200 rounded-md",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-red-600",
                                children: errors.submit
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                lineNumber: 264,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 263,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end gap-3 pt-4 border-t",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: onClose,
                                    className: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                    children: "取消"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 270,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "submit",
                                    disabled: loading,
                                    className: "px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",
                                    children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                                lineNumber: 284,
                                                columnNumber: 19
                                            }, this),
                                            "保存中..."
                                        ]
                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                                lineNumber: 289,
                                                columnNumber: 19
                                            }, this),
                                            "保存"
                                        ]
                                    }, void 0, true)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                                    lineNumber: 277,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
                    lineNumber: 150,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/tasks/TaskEditForm.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/tasks/TaskDeleteConfirm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TaskDeleteConfirm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
'use client';
;
;
;
function TaskDeleteConfirm({ task, isOpen, onClose, onConfirm }) {
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleConfirm = async ()=>{
        if (!task) return;
        setLoading(true);
        try {
            await onConfirm(task.id);
            onClose();
        } catch (error) {
            console.error('Failed to delete task:', error);
        } finally{
            setLoading(false);
        }
    };
    if (!isOpen || !task) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg shadow-xl max-w-md w-full",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between p-6 border-b",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                        className: "h-5 w-5 text-red-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                        lineNumber: 45,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                    lineNumber: 44,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-gray-900",
                                    children: "删除任务"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                    lineNumber: 47,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 43,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "p-2 hover:bg-gray-100 rounded-full transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-5 w-5 text-gray-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                    lineNumber: 42,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-700 mb-4",
                            children: [
                                "您确定要删除任务 ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-medium text-gray-900",
                                    children: [
                                        '"',
                                        task.title,
                                        '"'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                    lineNumber: 62,
                                    columnNumber: 22
                                }, this),
                                " 吗？"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 border border-red-200 rounded-md p-3 mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                        className: "h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                        lineNumber: 67,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-red-700",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium mb-1",
                                                children: "此操作无法撤销"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                lineNumber: 69,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "删除后，任务的所有相关数据将永久丢失，包括："
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                lineNumber: 70,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: "list-disc list-inside mt-1 space-y-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        children: "任务详细信息"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                        lineNumber: 72,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        children: "历史记录"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                        lineNumber: 73,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        children: "相关统计数据"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                        lineNumber: 74,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                lineNumber: 71,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                        lineNumber: 68,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                lineNumber: 66,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 65,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-50 rounded-md p-3 mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-sm font-medium text-gray-900 mb-2",
                                    children: "任务信息"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-1 text-sm text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "标题："
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 85,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: task.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 86,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                            lineNumber: 84,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "分类："
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 89,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: task.category === 'work' ? '工作' : task.category === 'improvement' ? '自我提升' : '娱乐'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 90,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                            lineNumber: 88,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "状态："
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 96,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: task.status === 'pending' ? '未开始' : task.status === 'in-progress' ? '进行中' : task.status === 'completed' ? '已完成' : '已推迟'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 97,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                            lineNumber: 95,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "截止时间："
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 104,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: task.deadline.toLocaleDateString('zh-CN')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                                    lineNumber: 105,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                            lineNumber: 103,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                    lineNumber: 60,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-end gap-3 p-6 border-t bg-gray-50",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: onClose,
                            disabled: loading,
                            className: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50",
                            children: "取消"
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 115,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: handleConfirm,
                            disabled: loading,
                            className: "px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",
                            children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                        lineNumber: 131,
                                        columnNumber: 17
                                    }, this),
                                    "删除中..."
                                ]
                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                                        lineNumber: 136,
                                        columnNumber: 17
                                    }, this),
                                    "确认删除"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                            lineNumber: 123,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
                    lineNumber: 114,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/tasks/TaskDeleteConfirm.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TaskList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-ssr] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js [app-ssr] (ecmascript) <export default as SortAsc>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2d$wide$2d$narrow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SortDesc$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js [app-ssr] (ecmascript) <export default as SortDesc>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskItem$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskItem.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskEditForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskEditForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskDeleteConfirm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskDeleteConfirm.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function TaskList({ tasks, loading = false, onCreateTask, onEditTask, onDeleteTask, onStatusChange, onStartTask, onCompleteTask }) {
    const [editingTask, setEditingTask] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [deletingTask, setDeletingTask] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showEditForm, setShowEditForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        status: 'all',
        category: 'all',
        search: ''
    });
    const [sortBy, setSortBy] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('deadline');
    const [sortDirection, setSortDirection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('asc');
    const [showFilters, setShowFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // 过滤和排序任务
    const filteredAndSortedTasks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        let filtered = tasks.filter((task)=>{
            // 状态过滤
            if (filters.status !== 'all' && task.status !== filters.status) {
                return false;
            }
            // 分类过滤
            if (filters.category !== 'all' && task.category !== filters.category) {
                return false;
            }
            // 搜索过滤
            if (filters.search) {
                const searchLower = filters.search.toLowerCase();
                return task.title.toLowerCase().includes(searchLower) || task.description && task.description.toLowerCase().includes(searchLower);
            }
            return true;
        });
        // 排序
        filtered.sort((a, b)=>{
            let comparison = 0;
            switch(sortBy){
                case 'deadline':
                    comparison = a.deadline.getTime() - b.deadline.getTime();
                    break;
                case 'priority':
                    // 按重要性和紧急性的综合分数排序
                    const scoreA = a.importance * 2 + a.urgency;
                    const scoreB = b.importance * 2 + b.urgency;
                    comparison = scoreB - scoreA;
                    break;
                case 'created':
                    comparison = a.createdAt.getTime() - b.createdAt.getTime();
                    break;
                case 'title':
                    comparison = a.title.localeCompare(b.title);
                    break;
            }
            return sortDirection === 'asc' ? comparison : -comparison;
        });
        return filtered;
    }, [
        tasks,
        filters,
        sortBy,
        sortDirection
    ]);
    const handleEdit = (task)=>{
        setEditingTask(task);
        setShowEditForm(true);
    };
    const handleDelete = (taskId)=>{
        const task = tasks.find((t)=>t.id === taskId);
        if (task) {
            setDeletingTask(task);
            setShowDeleteConfirm(true);
        }
    };
    const handleSaveEdit = async (updates)=>{
        if (editingTask) {
            await onEditTask(editingTask.id, updates);
        }
    };
    const handleSort = (option)=>{
        if (sortBy === option) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(option);
            setSortDirection('asc');
        }
    };
    const getStatusCount = (status)=>{
        return tasks.filter((task)=>task.status === status).length;
    };
    const getCategoryCount = (category)=>{
        return tasks.filter((task)=>task.category === category).length;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "任务管理"
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 147,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600",
                                children: [
                                    "共 ",
                                    tasks.length,
                                    " 个任务，",
                                    filteredAndSortedTasks.length,
                                    " 个符合条件"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 148,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 146,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: onCreateTask,
                        className: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this),
                            "新建任务"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/tasks/TaskList.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col lg:flex-row gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                            className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskList.tsx",
                                            lineNumber: 165,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "text",
                                            placeholder: "搜索任务标题或描述...",
                                            value: filters.search,
                                            onChange: (e)=>setFilters((prev)=>({
                                                        ...prev,
                                                        search: e.target.value
                                                    })),
                                            className: "w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tasks/TaskList.tsx",
                                            lineNumber: 166,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                                    lineNumber: 164,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 163,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setShowFilters(!showFilters),
                                className: "inline-flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 181,
                                        columnNumber: 13
                                    }, this),
                                    "筛选"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 177,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this),
                    showFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                        children: "状态"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 191,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: filters.status,
                                        onChange: (e)=>setFilters((prev)=>({
                                                    ...prev,
                                                    status: e.target.value
                                                })),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "all",
                                                children: [
                                                    "全部 (",
                                                    tasks.length,
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 197,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "pending",
                                                children: [
                                                    "未开始 (",
                                                    getStatusCount('pending'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 198,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "in-progress",
                                                children: [
                                                    "进行中 (",
                                                    getStatusCount('in-progress'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 199,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "completed",
                                                children: [
                                                    "已完成 (",
                                                    getStatusCount('completed'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 200,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "postponed",
                                                children: [
                                                    "已推迟 (",
                                                    getStatusCount('postponed'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 201,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 192,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 190,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                        children: "分类"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 207,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: filters.category,
                                        onChange: (e)=>setFilters((prev)=>({
                                                    ...prev,
                                                    category: e.target.value
                                                })),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "all",
                                                children: [
                                                    "全部 (",
                                                    tasks.length,
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 213,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "work",
                                                children: [
                                                    "工作 (",
                                                    getCategoryCount('work'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 214,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "improvement",
                                                children: [
                                                    "自我提升 (",
                                                    getCategoryCount('improvement'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 215,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "entertainment",
                                                children: [
                                                    "娱乐 (",
                                                    getCategoryCount('entertainment'),
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 216,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 208,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                        children: "排序"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                value: sortBy,
                                                onChange: (e)=>setSortBy(e.target.value),
                                                className: "flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "deadline",
                                                        children: "截止时间"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                        lineNumber: 229,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "priority",
                                                        children: "优先级"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                        lineNumber: 230,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "created",
                                                        children: "创建时间"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                        lineNumber: 231,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "title",
                                                        children: "标题"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                        lineNumber: 232,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 224,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc'),
                                                className: "px-2 py-2 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-50",
                                                children: sortDirection === 'asc' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__["SortAsc"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 46
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$down$2d$wide$2d$narrow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SortDesc$3e$__["SortDesc"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 80
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                                lineNumber: 234,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                                        lineNumber: 223,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 221,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setFilters({
                                            status: 'all',
                                            category: 'all',
                                            search: ''
                                        });
                                        setSortBy('deadline');
                                        setSortDirection('asc');
                                    },
                                    className: "w-full px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",
                                    children: "清除筛选"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                                    lineNumber: 245,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/tasks/TaskList.tsx",
                                lineNumber: 244,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/tasks/TaskList.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center py-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskList.tsx",
                            lineNumber: 264,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 mt-2",
                            children: "正在加载任务..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/tasks/TaskList.tsx",
                            lineNumber: 265,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                    lineNumber: 263,
                    columnNumber: 11
                }, this) : filteredAndSortedTasks.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center py-12 bg-white rounded-lg shadow",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-500 text-lg",
                        children: filters.search || filters.status !== 'all' || filters.category !== 'all' ? '没有找到符合条件的任务' : '还没有任务，点击上方按钮创建第一个任务吧！'
                    }, void 0, false, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 269,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/tasks/TaskList.tsx",
                    lineNumber: 268,
                    columnNumber: 11
                }, this) : filteredAndSortedTasks.map((task)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskItem$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        task: task,
                        onEdit: handleEdit,
                        onDelete: handleDelete,
                        onStatusChange: onStatusChange,
                        onStart: onStartTask,
                        onComplete: onCompleteTask
                    }, task.id, false, {
                        fileName: "[project]/src/components/tasks/TaskList.tsx",
                        lineNumber: 277,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/tasks/TaskList.tsx",
                lineNumber: 261,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskEditForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                task: editingTask,
                isOpen: showEditForm,
                onClose: ()=>{
                    setShowEditForm(false);
                    setEditingTask(null);
                },
                onSave: handleSaveEdit
            }, void 0, false, {
                fileName: "[project]/src/components/tasks/TaskList.tsx",
                lineNumber: 291,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskDeleteConfirm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                task: deletingTask,
                isOpen: showDeleteConfirm,
                onClose: ()=>{
                    setShowDeleteConfirm(false);
                    setDeletingTask(null);
                },
                onConfirm: onDeleteTask
            }, void 0, false, {
                fileName: "[project]/src/components/tasks/TaskList.tsx",
                lineNumber: 302,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/tasks/TaskList.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/tasks/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 任务管理组件统一导出
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskItem$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskItem.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskEditForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskEditForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskDeleteConfirm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskDeleteConfirm.tsx [app-ssr] (ecmascript)");
;
;
;
;
}}),
"[project]/src/components/tasks/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskItem$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskItem.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskEditForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskEditForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskDeleteConfirm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskDeleteConfirm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/tasks/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript) <export default as TaskList>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TaskList": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript)");
}}),
"[project]/src/app/tasks/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TasksPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useTaskStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/tasks/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TaskList$3e$__ = __turbopack_context__.i("[project]/src/components/tasks/TaskList.tsx [app-ssr] (ecmascript) <export default as TaskList>");
'use client';
;
;
;
;
;
;
function TasksPage() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const { tasks, loading, fetchTasks, updateTask, deleteTask, completeTask, startTask, pauseTask } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTaskStore"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!user) {
            router.push('/auth/signin');
            return;
        }
        // 获取任务列表
        fetchTasks(user.id);
    }, [
        user,
        router,
        fetchTasks
    ]);
    const handleCreateTask = ()=>{
        router.push('/tasks/new');
    };
    const handleEditTask = async (taskId, updates)=>{
        try {
            await updateTask(taskId, updates);
        } catch (error) {
            console.error('Failed to update task:', error);
            throw error;
        }
    };
    const handleDeleteTask = async (taskId)=>{
        try {
            await deleteTask(taskId);
        } catch (error) {
            console.error('Failed to delete task:', error);
            throw error;
        }
    };
    const handleStatusChange = async (taskId, status)=>{
        try {
            await updateTask(taskId, {
                status
            });
        } catch (error) {
            console.error('Failed to update task status:', error);
            throw error;
        }
    };
    const handleStartTask = async (taskId)=>{
        try {
            await startTask(taskId);
        } catch (error) {
            console.error('Failed to start task:', error);
            throw error;
        }
    };
    const handleCompleteTask = async (taskId)=>{
        try {
            await completeTask(taskId);
        } catch (error) {
            console.error('Failed to complete task:', error);
            throw error;
        }
    };
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/app/tasks/page.tsx",
                lineNumber: 86,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/tasks/page.tsx",
            lineNumber: 85,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center py-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>router.push('/dashboard'),
                                        className: "text-gray-500 hover:text-gray-700 mr-4",
                                        children: "← 返回仪表板"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/page.tsx",
                                        lineNumber: 98,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold text-gray-900",
                                        children: "任务管理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/page.tsx",
                                        lineNumber: 104,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/page.tsx",
                                lineNumber: 97,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm text-gray-600",
                                        children: [
                                            "欢迎，",
                                            user.email
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/tasks/page.tsx",
                                        lineNumber: 107,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>router.push('/dashboard'),
                                        className: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",
                                        children: "仪表板"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/page.tsx",
                                        lineNumber: 110,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/page.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/tasks/page.tsx",
                        lineNumber: 96,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/tasks/page.tsx",
                    lineNumber: 95,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/tasks/page.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tasks$2f$TaskList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TaskList$3e$__["TaskList"], {
                    tasks: tasks,
                    loading: loading,
                    onCreateTask: handleCreateTask,
                    onEditTask: handleEditTask,
                    onDeleteTask: handleDeleteTask,
                    onStatusChange: handleStatusChange,
                    onStartTask: handleStartTask,
                    onCompleteTask: handleCompleteTask
                }, void 0, false, {
                    fileName: "[project]/src/app/tasks/page.tsx",
                    lineNumber: 123,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/tasks/page.tsx",
                lineNumber: 122,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/tasks/page.tsx",
        lineNumber: 92,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__de7d04d7._.js.map